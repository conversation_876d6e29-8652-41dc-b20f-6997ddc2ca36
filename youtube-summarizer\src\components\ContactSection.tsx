import React, { useState } from 'react';
import { 
  EnvelopeIcon, 
  PhoneIcon, 
  MapPinIcon, 
  ChatBubbleLeftRightIcon,
  ClockIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

const ContactSection: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  const containerStyle = {
    maxWidth: '1200px',
    margin: '0 auto',
    padding: '0 20px'
  };

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!formData.name.trim()) newErrors.name = 'Name is required';
    if (!formData.email.trim()) newErrors.email = 'Email is required';
    else if (!/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = 'Email is invalid';
    if (!formData.subject.trim()) newErrors.subject = 'Subject is required';
    if (!formData.message.trim()) newErrors.message = 'Message is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      // Here you would typically make an API call to send the message
      console.log('Contact form data:', formData);
      setIsSubmitted(true);
      // Reset form after 3 seconds
      setTimeout(() => {
        setIsSubmitted(false);
        setFormData({ name: '', email: '', subject: '', message: '' });
      }, 3000);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const inputStyle = {
    width: '100%',
    padding: '12px 16px',
    border: '2px solid #e5e7eb',
    borderRadius: '8px',
    fontSize: '16px',
    transition: 'border-color 0.2s ease',
    outline: 'none',
    fontFamily: 'inherit'
  };

  const inputErrorStyle = {
    ...inputStyle,
    borderColor: '#ef4444'
  };

  const buttonStyle = {
    background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
    color: 'white',
    padding: '12px 32px',
    border: 'none',
    borderRadius: '8px',
    fontSize: '16px',
    fontWeight: '600',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
  };

  const contactInfo = [
    {
      icon: EnvelopeIcon,
      title: 'Email Us',
      content: '<EMAIL>',
      description: 'Send us an email anytime'
    },
    {
      icon: PhoneIcon,
      title: 'Call Us',
      content: '+****************',
      description: 'Mon-Fri from 8am to 5pm'
    },
    {
      icon: MapPinIcon,
      title: 'Visit Us',
      content: '123 Innovation Drive, Tech City, TC 12345',
      description: 'Come say hello at our office'
    },
    {
      icon: ClockIcon,
      title: 'Support Hours',
      content: '24/7 Online Support',
      description: 'We\'re here to help anytime'
    }
  ];

  if (isSubmitted) {
    return (
      <div id="contact" style={{padding: '80px 20px', backgroundColor: '#f9fafb'}}>
        <div style={containerStyle}>
          <div style={{
            textAlign: 'center',
            backgroundColor: 'white',
            padding: '60px 40px',
            borderRadius: '12px',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
          }}>
            <CheckCircleIcon style={{
              height: '64px',
              width: '64px',
              color: '#10b981',
              margin: '0 auto 20px'
            }} />
            <h3 style={{fontSize: '24px', color: '#1f2937', marginBottom: '12px', fontWeight: 'bold'}}>
              Message Sent Successfully!
            </h3>
            <p style={{color: '#6b7280', fontSize: '16px'}}>
              Thank you for contacting us. We'll get back to you within 24 hours.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div id="contact" style={{padding: '80px 20px', backgroundColor: '#f9fafb'}}>
      <div style={containerStyle}>
        {/* Header */}
        <div style={{textAlign: 'center', marginBottom: '60px'}}>
          <h2 style={{fontSize: '36px', marginBottom: '16px', color: '#1f2937', fontWeight: 'bold'}}>
            Get in Touch
          </h2>
          <p style={{fontSize: '18px', color: '#6b7280', maxWidth: '600px', margin: '0 auto'}}>
            Have questions about VidSum? We'd love to hear from you. Send us a message and we'll respond as soon as possible.
          </p>
        </div>

        <div style={{display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '40px'}}>
          {/* Contact Information */}
          <div>
            <h3 style={{fontSize: '24px', marginBottom: '30px', color: '#1f2937', fontWeight: 'bold'}}>
              Contact Information
            </h3>
            
            <div style={{display: 'flex', flexDirection: 'column', gap: '24px'}}>
              {contactInfo.map((info, index) => {
                const IconComponent = info.icon;
                return (
                  <div key={index} style={{
                    display: 'flex',
                    alignItems: 'flex-start',
                    padding: '20px',
                    backgroundColor: 'white',
                    borderRadius: '12px',
                    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                    transition: 'transform 0.2s ease'
                  }}
                  onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-2px)'}
                  onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}
                  >
                    <div style={{
                      width: '48px',
                      height: '48px',
                      background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
                      borderRadius: '12px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginRight: '16px',
                      flexShrink: 0
                    }}>
                      <IconComponent style={{height: '24px', width: '24px', color: 'white'}} />
                    </div>
                    <div>
                      <h4 style={{fontSize: '18px', fontWeight: '600', color: '#1f2937', marginBottom: '4px'}}>
                        {info.title}
                      </h4>
                      <p style={{fontSize: '16px', color: '#3b82f6', marginBottom: '4px', fontWeight: '500'}}>
                        {info.content}
                      </p>
                      <p style={{fontSize: '14px', color: '#6b7280'}}>
                        {info.description}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* FAQ Link */}
            <div style={{
              marginTop: '30px',
              padding: '20px',
              backgroundColor: 'white',
              borderRadius: '12px',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
              textAlign: 'center'
            }}>
              <ChatBubbleLeftRightIcon style={{
                height: '32px',
                width: '32px',
                color: '#3b82f6',
                margin: '0 auto 12px'
              }} />
              <h4 style={{fontSize: '18px', fontWeight: '600', color: '#1f2937', marginBottom: '8px'}}>
                Frequently Asked Questions
              </h4>
              <p style={{fontSize: '14px', color: '#6b7280', marginBottom: '16px'}}>
                Find quick answers to common questions
              </p>
              <button style={{
                color: '#3b82f6',
                textDecoration: 'underline',
                border: 'none',
                background: 'transparent',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500'
              }}>
                View FAQ →
              </button>
            </div>
          </div>

          {/* Contact Form */}
          <div style={{
            backgroundColor: 'white',
            padding: '40px',
            borderRadius: '12px',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
          }}>
            <h3 style={{fontSize: '24px', marginBottom: '24px', color: '#1f2937', fontWeight: 'bold'}}>
              Send us a Message
            </h3>

            <form onSubmit={handleSubmit}>
              {/* Name Field */}
              <div style={{marginBottom: '20px'}}>
                <label style={{display: 'block', marginBottom: '6px', color: '#374151', fontWeight: '500'}}>
                  Full Name
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  style={errors.name ? inputErrorStyle : inputStyle}
                  onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                  onBlur={(e) => e.target.style.borderColor = errors.name ? '#ef4444' : '#e5e7eb'}
                  placeholder="Your full name"
                />
                {errors.name && (
                  <p style={{color: '#ef4444', fontSize: '14px', marginTop: '4px'}}>{errors.name}</p>
                )}
              </div>

              {/* Email Field */}
              <div style={{marginBottom: '20px'}}>
                <label style={{display: 'block', marginBottom: '6px', color: '#374151', fontWeight: '500'}}>
                  Email Address
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  style={errors.email ? inputErrorStyle : inputStyle}
                  onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                  onBlur={(e) => e.target.style.borderColor = errors.email ? '#ef4444' : '#e5e7eb'}
                  placeholder="<EMAIL>"
                />
                {errors.email && (
                  <p style={{color: '#ef4444', fontSize: '14px', marginTop: '4px'}}>{errors.email}</p>
                )}
              </div>

              {/* Subject Field */}
              <div style={{marginBottom: '20px'}}>
                <label style={{display: 'block', marginBottom: '6px', color: '#374151', fontWeight: '500'}}>
                  Subject
                </label>
                <input
                  type="text"
                  value={formData.subject}
                  onChange={(e) => handleInputChange('subject', e.target.value)}
                  style={errors.subject ? inputErrorStyle : inputStyle}
                  onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                  onBlur={(e) => e.target.style.borderColor = errors.subject ? '#ef4444' : '#e5e7eb'}
                  placeholder="What's this about?"
                />
                {errors.subject && (
                  <p style={{color: '#ef4444', fontSize: '14px', marginTop: '4px'}}>{errors.subject}</p>
                )}
              </div>

              {/* Message Field */}
              <div style={{marginBottom: '30px'}}>
                <label style={{display: 'block', marginBottom: '6px', color: '#374151', fontWeight: '500'}}>
                  Message
                </label>
                <textarea
                  value={formData.message}
                  onChange={(e) => handleInputChange('message', e.target.value)}
                  style={{
                    ...inputStyle,
                    minHeight: '120px',
                    resize: 'vertical',
                    borderColor: errors.message ? '#ef4444' : '#e5e7eb'
                  }}
                  onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                  onBlur={(e) => e.target.style.borderColor = errors.message ? '#ef4444' : '#e5e7eb'}
                  placeholder="Tell us how we can help you..."
                />
                {errors.message && (
                  <p style={{color: '#ef4444', fontSize: '14px', marginTop: '4px'}}>{errors.message}</p>
                )}
              </div>

              {/* Submit Button */}
              <button
                type="submit"
                style={buttonStyle}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-1px)';
                  e.currentTarget.style.boxShadow = '0 6px 12px rgba(0, 0, 0, 0.15)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1)';
                }}
              >
                Send Message
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactSection;
