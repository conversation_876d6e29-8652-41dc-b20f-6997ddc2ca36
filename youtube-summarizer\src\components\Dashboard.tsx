import React, { useState } from 'react';
import { 
  PlayIcon, 
  DocumentTextIcon, 
  CloudDownloadIcon, 
  UserIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
  PlusIcon,
  ClockIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import { useNavigate } from 'react-router-dom';

const Dashboard: React.FC = () => {
  const [videoUrl, setVideoUrl] = useState('');
  const navigate = useNavigate();

  const recentSummaries = [
    {
      id: 1,
      title: 'How to Build a React App with TypeScript',
      url: 'https://youtube.com/watch?v=example1',
      status: 'completed',
      createdAt: '2 hours ago',
      duration: '15:30'
    },
    {
      id: 2,
      title: 'Advanced JavaScript Concepts Explained',
      url: 'https://youtube.com/watch?v=example2',
      status: 'processing',
      createdAt: '1 hour ago',
      duration: '22:45'
    },
    {
      id: 3,
      title: 'CSS Grid vs Flexbox: Complete Guide',
      url: 'https://youtube.com/watch?v=example3',
      status: 'completed',
      createdAt: '3 hours ago',
      duration: '18:20'
    }
  ];

  const handleLogout = () => {
    navigate('/');
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (videoUrl) {
      // Simulate processing
      console.log('Processing video:', videoUrl);
      setVideoUrl('');
    }
  };

  return (
    <div className="min-h-screen bg-secondary-50">
      {/* Navigation */}
      <nav className="bg-white border-b border-secondary-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <PlayIcon className="h-8 w-8 text-primary-600" />
              <span className="ml-2 text-xl font-bold text-secondary-900">VideoSummarizer</span>
            </div>
            <div className="flex items-center space-x-4">
              <button className="p-2 text-secondary-600 hover:text-primary-600 rounded-lg hover:bg-secondary-100 transition-colors">
                <Cog6ToothIcon className="h-5 w-5" />
              </button>
              <button className="p-2 text-secondary-600 hover:text-primary-600 rounded-lg hover:bg-secondary-100 transition-colors">
                <UserIcon className="h-5 w-5" />
              </button>
              <button 
                onClick={handleLogout}
                className="p-2 text-secondary-600 hover:text-red-600 rounded-lg hover:bg-secondary-100 transition-colors"
              >
                <ArrowRightOnRectangleIcon className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-secondary-900 mb-2">Dashboard</h1>
          <p className="text-secondary-600">Transform YouTube videos into instant summaries</p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="card p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                <DocumentTextIcon className="h-6 w-6 text-primary-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-secondary-600">Total Summaries</p>
                <p className="text-2xl font-bold text-secondary-900">24</p>
              </div>
            </div>
          </div>
          
          <div className="card p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <ClockIcon className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-secondary-600">Time Saved</p>
                <p className="text-2xl font-bold text-secondary-900">8.5h</p>
              </div>
            </div>
          </div>
          
          <div className="card p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <CloudDownloadIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-secondary-600">Downloads</p>
                <p className="text-2xl font-bold text-secondary-900">18</p>
              </div>
            </div>
          </div>
        </div>

        {/* New Summary Form */}
        <div className="card p-6 mb-8">
          <h2 className="text-xl font-semibold text-secondary-900 mb-4">Create New Summary</h2>
          <form onSubmit={handleSubmit} className="flex gap-4">
            <div className="flex-1">
              <input
                type="url"
                value={videoUrl}
                onChange={(e) => setVideoUrl(e.target.value)}
                placeholder="Paste YouTube video URL here..."
                className="w-full px-4 py-3 border border-secondary-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
            <button
              type="submit"
              disabled={!videoUrl}
              className="btn-primary flex items-center px-6 py-3 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <PlusIcon className="h-5 w-5 mr-2" />
              Generate Summary
            </button>
          </form>
        </div>

        {/* Recent Summaries */}
        <div className="card p-6">
          <h2 className="text-xl font-semibold text-secondary-900 mb-6">Recent Summaries</h2>
          <div className="space-y-4">
            {recentSummaries.map((summary) => (
              <div key={summary.id} className="flex items-center justify-between p-4 border border-secondary-200 rounded-lg hover:bg-secondary-50 transition-colors">
                <div className="flex items-center space-x-4">
                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                    summary.status === 'completed' 
                      ? 'bg-green-100' 
                      : 'bg-yellow-100'
                  }`}>
                    {summary.status === 'completed' ? (
                      <CheckCircleIcon className="h-5 w-5 text-green-600" />
                    ) : (
                      <ClockIcon className="h-5 w-5 text-yellow-600" />
                    )}
                  </div>
                  <div>
                    <h3 className="font-medium text-secondary-900">{summary.title}</h3>
                    <p className="text-sm text-secondary-600">
                      {summary.createdAt} • {summary.duration}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {summary.status === 'completed' && (
                    <>
                      <button className="btn-secondary text-sm px-3 py-1">
                        View Summary
                      </button>
                      <button className="btn-secondary text-sm px-3 py-1">
                        Download
                      </button>
                    </>
                  )}
                  {summary.status === 'processing' && (
                    <span className="text-sm text-yellow-600 font-medium">Processing...</span>
                  )}
                </div>
              </div>
            ))}
          </div>
          
          {recentSummaries.length === 0 && (
            <div className="text-center py-12">
              <DocumentTextIcon className="h-12 w-12 text-secondary-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-secondary-900 mb-2">No summaries yet</h3>
              <p className="text-secondary-600">Create your first summary by pasting a YouTube URL above.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
