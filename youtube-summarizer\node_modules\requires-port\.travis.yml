language: node_js
node_js:
  - "0.12"
  - "0.11"
  - "0.10"
  - "0.9"
  - "0.8"
  - "iojs-v1.1"
  - "iojs-v1.0"
before_install:
  - "npm install -g npm@1.4.x"
script:
  - "npm run test-travis"
after_script:
  - "npm install coveralls@2.11.x && cat coverage/lcov.info | coveralls"
matrix:
  fast_finish: true
  allow_failures:
    - node_js: "0.11"
    - node_js: "0.9"
    - node_js: "iojs-v1.1"
    - node_js: "iojs-v1.0"
notifications:
  irc:
    channels:
      - "irc.freenode.org#unshift"
    on_success: change
    on_failure: change
