{"version": 3, "file": "m3u8-parser.js", "sourceRoot": "", "sources": ["../src/m3u8-parser.ts"], "names": [], "mappings": ";;AAAA,mCAAkC;AAIlC;;GAEG;AACH,MAAqB,UAAW,SAAQ,iBAAQ;IAO9C;QACE,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;QACd,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;YACrB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAChC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,cAAc,CAAC,KAAa;QAClC,IAAI,KAAK,GAA8B,EAAE,CAAC;QAC1C,IAAI,KAAK,GAAG,uCAAuC,CAAC;QACpD,IAAI,KAAK,CAAC;QACV,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,IAAI,EAAE;YAC3C,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;SACxC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,WAAW,CAAC,KAAa;QAC/B,IAAI,CAAC,KAAK;YAAE,OAAO,IAAI,CAAC;QACxB,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAI,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;QACzE,IAAI,GAAG,GAAG,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAI,KAAK,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC,GAAG,CAAC;QACnC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,UAAU,CAAC,IAAY;QACrB,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACtD,IAAI,KAAK,EAAE;YACT,iBAAiB;YACjB,MAAM,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACrB,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAC7B,QAAQ,GAAG,EAAE;gBACX,KAAK,yBAAyB;oBAC5B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;oBAClD,MAAM;gBACR,KAAK,sBAAsB;oBACzB,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;oBAC5B,MAAM;gBACR,KAAK,WAAW,CAAC,CAAC;oBAChB,IAAI,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;oBACvC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;wBACd,IAAI,CAAC,OAAO,CACV,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC,CAAC;wBACnE,OAAO;qBACR;oBACD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;wBAChB,GAAG,EAAE,KAAK,CAAC,GAAG;wBACd,GAAG,EAAE,IAAI,CAAC,IAAI;wBACd,IAAI,EAAE,IAAI;wBACV,QAAQ,EAAE,CAAC;wBACX,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC;qBACzC,CAAC,CAAC;oBACH,MAAM;iBACP;gBACD,KAAK,iBAAiB,CAAC,CAAC;oBACtB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;oBAC9C,MAAM;iBACP;gBACD,KAAK,QAAQ;oBACX,IAAI,CAAC,iBAAiB;wBACpB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;oBACrD,MAAM;gBACR,KAAK,eAAe;oBAClB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBACrB,MAAM;aACT;SACF;aAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,EAAE;YAC1C,oBAAoB;YACpB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBAChB,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE;gBAChB,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE;gBAChB,QAAQ,EAAE,IAAI,CAAC,iBAAiB;gBAChC,KAAK,EAAE,IAAI,CAAC,cAAc;aAC3B,CAAC,CAAC;YACH,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;SAC5B;IACH,CAAC;IAED,MAAM,CAAC,KAAa,EAAE,QAAgB,EAAE,QAAoB;QAC1D,IAAI,KAAK,GAAa,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACzD,IAAI,IAAI,CAAC,SAAS,EAAE;YAAE,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;SAAE;QAC7D,KAAK,CAAC,OAAO,CAAC,CAAC,IAAY,EAAE,CAAS,EAAE,EAAE;YACxC,IAAI,IAAI,CAAC,SAAS;gBAAE,OAAO;YAC3B,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;gBACxB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;aACvB;iBAAM;gBACL,oDAAoD;gBACpD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;aACvB;QACH,CAAC,CAAC,CAAC;QACH,QAAQ,EAAE,CAAC;IACb,CAAC;CACF;AA3GD,6BA2GC"}