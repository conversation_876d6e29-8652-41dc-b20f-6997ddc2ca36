import React, { useState } from 'react';

function App() {
  const [currentPage, setCurrentPage] = useState('home');
  const [dashboardSection, setDashboardSection] = useState('overview');

  const buttonStyle = {
    backgroundColor: '#3b82f6',
    color: 'white',
    padding: '12px 24px',
    border: 'none',
    borderRadius: '8px',
    fontSize: '16px',
    cursor: 'pointer',
    margin: '5px',
    transition: 'all 0.3s ease'
  };

  const navStyle = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '15px 30px',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    backdropFilter: 'blur(10px)',
    borderBottom: '1px solid rgba(229, 231, 235, 0.3)',
    position: 'fixed' as const,
    top: '0',
    left: '0',
    right: '0',
    zIndex: 1000,
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05)'
  };

  const navLinkStyle = {
    color: '#374151',
    textDecoration: 'none',
    padding: '8px 16px',
    borderRadius: '6px',
    transition: 'all 0.3s ease',
    cursor: 'pointer',
    fontWeight: '500'
  };

  const containerStyle = {
    maxWidth: '1200px',
    margin: '0 auto',
    padding: '0 20px'
  };

  if (currentPage === 'login') {
    return (
      <div style={{paddingTop: '80px', minHeight: '100vh', backgroundColor: '#f8fafc'}}>
        <nav style={navStyle}>
          <div style={{fontSize: '24px', fontWeight: 'bold', color: '#1f2937'}}>
            📹 VideoSummarizer
          </div>
          <button 
            style={{...buttonStyle, backgroundColor: '#6b7280'}}
            onClick={() => setCurrentPage('home')}
          >
            Back to Home
          </button>
        </nav>
        <div style={{...containerStyle, padding: '40px 20px'}}>
          <div style={{maxWidth: '400px', margin: '0 auto', backgroundColor: 'white', padding: '40px', borderRadius: '12px', boxShadow: '0 4px 6px rgba(0,0,0,0.1)'}}>
            <h2 style={{fontSize: '28px', marginBottom: '20px', textAlign: 'center', color: '#1f2937'}}>Welcome Back</h2>
            <p style={{textAlign: 'center', color: '#6b7280', marginBottom: '30px'}}>Sign in to your account</p>
            
            <div style={{marginBottom: '20px'}}>
              <label style={{display: 'block', marginBottom: '8px', fontWeight: '500', color: '#374151'}}>Email</label>
              <input 
                type="email" 
                placeholder="Enter your email"
                style={{
                  width: '100%',
                  padding: '12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '16px',
                  boxSizing: 'border-box'
                }}
              />
            </div>
            
            <div style={{marginBottom: '30px'}}>
              <label style={{display: 'block', marginBottom: '8px', fontWeight: '500', color: '#374151'}}>Password</label>
              <input 
                type="password" 
                placeholder="Enter your password"
                style={{
                  width: '100%',
                  padding: '12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '16px',
                  boxSizing: 'border-box'
                }}
              />
            </div>
            
            <button 
              style={{...buttonStyle, width: '100%', marginBottom: '20px'}}
              onClick={() => setCurrentPage('dashboard')}
            >
              Sign In
            </button>
            
            <p style={{textAlign: 'center', color: '#6b7280'}}>
              Don't have an account? <span style={{color: '#3b82f6', cursor: 'pointer'}}>Sign up</span>
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (currentPage === 'dashboard') {
    const sidebarItems = [
      { id: 'overview', icon: '📊', label: 'Overview' },
      { id: 'summaries', icon: '📝', label: 'My Summaries' },
      { id: 'storage', icon: '💾', label: 'Storage' },
      { id: 'api', icon: '🔌', label: 'API Keys' },
      { id: 'settings', icon: '⚙️', label: 'Settings' }
    ];

    const renderDashboardContent = () => {
      switch (dashboardSection) {
        case 'overview':
          return (
            <div>
              <h1 style={{fontSize: '32px', marginBottom: '10px', color: '#1f2937'}}>Dashboard Overview</h1>
              <p style={{color: '#6b7280', marginBottom: '40px'}}>Transform YouTube videos into instant summaries</p>
              
              <div style={{display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '20px', marginBottom: '40px'}}>
                <div style={{backgroundColor: 'white', padding: '24px', borderRadius: '12px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)'}}>
                  <h3 style={{color: '#1f2937', marginBottom: '8px'}}>Total Summaries</h3>
                  <p style={{fontSize: '32px', fontWeight: 'bold', color: '#3b82f6', margin: '0'}}>24</p>
                </div>
                <div style={{backgroundColor: 'white', padding: '24px', borderRadius: '12px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)'}}>
                  <h3 style={{color: '#1f2937', marginBottom: '8px'}}>Time Saved</h3>
                  <p style={{fontSize: '32px', fontWeight: 'bold', color: '#10b981', margin: '0'}}>8.5h</p>
                </div>
                <div style={{backgroundColor: 'white', padding: '24px', borderRadius: '12px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)'}}>
                  <h3 style={{color: '#1f2937', marginBottom: '8px'}}>Downloads</h3>
                  <p style={{fontSize: '32px', fontWeight: 'bold', color: '#8b5cf6', margin: '0'}}>18</p>
                </div>
              </div>
              
              <div style={{backgroundColor: 'white', padding: '30px', borderRadius: '12px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)', marginBottom: '30px'}}>
                <h2 style={{fontSize: '24px', marginBottom: '20px', color: '#1f2937'}}>Create New Summary</h2>
                <div style={{display: 'flex', gap: '15px', flexWrap: 'wrap'}}>
                  <input 
                    type="url" 
                    placeholder="Paste YouTube video URL here..."
                    style={{
                      flex: '1',
                      minWidth: '300px',
                      padding: '15px',
                      border: '1px solid #d1d5db',
                      borderRadius: '8px',
                      fontSize: '16px'
                    }}
                  />
                  <button style={{...buttonStyle, padding: '15px 30px'}}>
                    Generate Summary
                  </button>
                </div>
              </div>
            </div>
          );
        
        case 'summaries':
          return (
            <div>
              <h1 style={{fontSize: '32px', marginBottom: '10px', color: '#1f2937'}}>My Summaries</h1>
              <p style={{color: '#6b7280', marginBottom: '40px'}}>Manage all your video summaries</p>
              
              <div style={{backgroundColor: 'white', padding: '30px', borderRadius: '12px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)'}}>
                <h2 style={{fontSize: '24px', marginBottom: '20px', color: '#1f2937'}}>Recent Summaries</h2>
                {[
                  {title: 'How to Build a React App with TypeScript', status: 'completed', time: '2 hours ago'},
                  {title: 'Advanced JavaScript Concepts Explained', status: 'processing', time: '1 hour ago'},
                  {title: 'CSS Grid vs Flexbox: Complete Guide', status: 'completed', time: '3 hours ago'}
                ].map((item, index) => (
                  <div key={index} style={{
                    display: 'flex', 
                    justifyContent: 'space-between', 
                    alignItems: 'center',
                    padding: '15px',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    marginBottom: '10px'
                  }}>
                    <div>
                      <h3 style={{margin: '0 0 5px 0', color: '#1f2937'}}>{item.title}</h3>
                      <p style={{margin: '0', color: '#6b7280', fontSize: '14px'}}>{item.time}</p>
                    </div>
                    <div>
                      {item.status === 'completed' ? (
                        <span style={{color: '#10b981', fontWeight: '500'}}>✅ Completed</span>
                      ) : (
                        <span style={{color: '#f59e0b', fontWeight: '500'}}>⏳ Processing</span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          );
        
        case 'storage':
          return (
            <div>
              <h1 style={{fontSize: '32px', marginBottom: '10px', color: '#1f2937'}}>Storage Management</h1>
              <p style={{color: '#6b7280', marginBottom: '40px'}}>Monitor your storage usage and manage files</p>
              
              <div style={{backgroundColor: 'white', padding: '30px', borderRadius: '12px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)'}}>
                <h2 style={{fontSize: '24px', marginBottom: '20px', color: '#1f2937'}}>Storage Usage</h2>
                <div style={{backgroundColor: '#f3f4f6', borderRadius: '10px', height: '10px', marginBottom: '10px'}}>
                  <div style={{backgroundColor: '#3b82f6', borderRadius: '10px', height: '10px', width: '65%'}}></div>
                </div>
                <p style={{color: '#6b7280', fontSize: '14px', margin: '0'}}>6.5 GB of 10 GB used</p>
              </div>
            </div>
          );
        
        case 'api':
          return (
            <div>
              <h1 style={{fontSize: '32px', marginBottom: '10px', color: '#1f2937'}}>API Management</h1>
              <p style={{color: '#6b7280', marginBottom: '40px'}}>Manage your API keys and integration settings</p>
              
              <div style={{backgroundColor: 'white', padding: '30px', borderRadius: '12px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)'}}>
                <h2 style={{fontSize: '24px', marginBottom: '20px', color: '#1f2937'}}>API Keys</h2>
                <div style={{marginBottom: '20px'}}>
                  <label style={{display: 'block', marginBottom: '8px', fontWeight: '500', color: '#374151'}}>Production API Key</label>
                  <div style={{display: 'flex', gap: '10px'}}>
                    <input 
                      type="text" 
                      value="sk-proj-xxxxxxxxxxxxxxxxxxxxxxxx"
                      readOnly
                      style={{
                        flex: '1',
                        padding: '12px',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        fontSize: '14px',
                        backgroundColor: '#f9fafb'
                      }}
                    />
                    <button style={{...buttonStyle, padding: '12px 20px'}}>Copy</button>
                  </div>
                </div>
              </div>
            </div>
          );
        
        case 'settings':
          return (
            <div>
              <h1 style={{fontSize: '32px', marginBottom: '10px', color: '#1f2937'}}>Settings</h1>
              <p style={{color: '#6b7280', marginBottom: '40px'}}>Manage your account and preferences</p>
              
              <div style={{backgroundColor: 'white', padding: '30px', borderRadius: '12px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)'}}>
                <h2 style={{fontSize: '24px', marginBottom: '20px', color: '#1f2937'}}>Profile Settings</h2>
                <div style={{marginBottom: '20px'}}>
                  <label style={{display: 'block', marginBottom: '8px', fontWeight: '500', color: '#374151'}}>Full Name</label>
                  <input 
                    type="text" 
                    defaultValue="John Doe"
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '1px solid #d1d5db',
                      borderRadius: '8px',
                      fontSize: '16px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
                <button style={buttonStyle}>Save Changes</button>
              </div>
            </div>
          );
        
        default:
          return <div>Page not found</div>;
      }
    };

    return (
      <div style={{display: 'flex', minHeight: '100vh', backgroundColor: '#f8fafc'}}>
        <div style={{
          width: '280px',
          backgroundColor: 'white',
          borderRight: '1px solid #e5e7eb',
          padding: '20px 0',
          position: 'fixed',
          height: '100vh',
          overflowY: 'auto'
        }}>
          <div style={{padding: '0 20px', marginBottom: '30px'}}>
            <div style={{fontSize: '24px', fontWeight: 'bold', color: '#1f2937'}}>
              📹 VideoSummarizer
            </div>
          </div>
          
          <nav>
            {sidebarItems.map((item) => (
              <button
                key={item.id}
                onClick={() => setDashboardSection(item.id)}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  width: '100%',
                  padding: '12px 20px',
                  border: 'none',
                  backgroundColor: dashboardSection === item.id ? '#eff6ff' : 'transparent',
                  color: dashboardSection === item.id ? '#3b82f6' : '#374151',
                  cursor: 'pointer',
                  fontSize: '16px',
                  textAlign: 'left',
                  transition: 'all 0.3s ease',
                  borderLeft: dashboardSection === item.id ? '3px solid #3b82f6' : '3px solid transparent'
                }}
              >
                <span style={{marginRight: '12px', fontSize: '18px'}}>{item.icon}</span>
                {item.label}
              </button>
            ))}
          </nav>
          
          <div style={{position: 'absolute', bottom: '20px', left: '20px', right: '20px'}}>
            <button 
              style={{
                ...buttonStyle,
                backgroundColor: '#ef4444',
                width: '100%',
                margin: '0'
              }}
              onClick={() => setCurrentPage('home')}
            >
              🚪 Logout
            </button>
          </div>
        </div>

        <div style={{marginLeft: '280px', flex: 1, padding: '40px'}}>
          {renderDashboardContent()}
        </div>
      </div>
    );
  }

  return (
    <div>
      <nav style={navStyle}>
        <div style={{display: 'flex', alignItems: 'center'}}>
          <div style={{fontSize: '28px', fontWeight: 'bold', color: '#1f2937', marginRight: '40px'}}>
            📹 VideoSummarizer
          </div>
        </div>
        <div style={{display: 'flex', alignItems: 'center', gap: '15px'}}>
          <button 
            style={{
              ...buttonStyle,
              margin: '0',
              padding: '8px 20px',
              fontSize: '14px',
              fontWeight: '600'
            }}
            onClick={() => setCurrentPage('login')}
          >
            Get Started Free
          </button>
        </div>
      </nav>

      <div style={{paddingTop: '80px'}}>
        <div style={{background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white', padding: '80px 20px', textAlign: 'center'}}>
          <div style={containerStyle}>
            <h1 style={{fontSize: '48px', marginBottom: '20px', fontWeight: 'bold'}}>
              Transform YouTube Videos into
              <br />
              <span style={{color: '#fbbf24'}}>Instant Summaries</span>
            </h1>
            <p style={{fontSize: '20px', marginBottom: '40px', opacity: '0.9'}}>
              Extract key insights, download captions, and get AI-powered summaries from any YouTube video in seconds.
            </p>
            <button 
              style={{...buttonStyle, fontSize: '18px', padding: '15px 30px', backgroundColor: '#fbbf24', color: '#1f2937'}}
              onClick={() => setCurrentPage('login')}
            >
              Get Started Free
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
