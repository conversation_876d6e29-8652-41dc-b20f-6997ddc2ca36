import React, { useState } from 'react';
import { 
  PlayIcon, 
  DocumentTextIcon, 
  CloudArrowDownIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

interface VideoProcessorProps {
  onBack: () => void;
}

interface VideoData {
  id: string;
  title: string;
  thumbnail: string;
  duration: string;
  channelName: string;
  embedUrl: string;
}

interface CaptionData {
  text: string;
  start: number;
  duration: number;
}

const VideoProcessor: React.FC<VideoProcessorProps> = ({ onBack }) => {
  const [youtubeUrl, setYoutubeUrl] = useState('');
  const [videoData, setVideoData] = useState<VideoData | null>(null);
  const [captions, setCaptions] = useState<CaptionData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [isExtractingCaptions, setIsExtractingCaptions] = useState(false);
  const [captionsExtracted, setCaptionsExtracted] = useState(false);

  // Extract YouTube video ID from URL
  const extractVideoId = (url: string): string | null => {
    const patterns = [
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
      /youtube\.com\/watch\?.*v=([^&\n?#]+)/
    ];
    
    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) return match[1];
    }
    return null;
  };

  // Simulate fetching video data (in real app, this would call YouTube API)
  const fetchVideoData = async (videoId: string): Promise<VideoData> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Mock video data (in real app, fetch from YouTube API)
    return {
      id: videoId,
      title: "Sample YouTube Video Title - How to Build Amazing Apps",
      thumbnail: `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,
      duration: "15:42",
      channelName: "Tech Channel",
      embedUrl: `https://www.youtube.com/embed/${videoId}`
    };
  };

  // Simulate extracting captions (in real app, this would call YouTube API or transcript service)
  const extractCaptions = async (videoId: string): Promise<CaptionData[]> => {
    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Mock caption data
    return [
      { text: "Welcome to this tutorial on building amazing applications.", start: 0, duration: 3.5 },
      { text: "Today we'll be covering the fundamentals of modern web development.", start: 3.5, duration: 4.2 },
      { text: "First, let's start with setting up our development environment.", start: 7.7, duration: 3.8 },
      { text: "We'll need to install Node.js and npm for our project.", start: 11.5, duration: 3.2 },
      { text: "Once that's done, we can create our first React application.", start: 14.7, duration: 4.1 },
      { text: "The process is straightforward and we'll walk through each step.", start: 18.8, duration: 3.9 },
      { text: "Remember to follow along and pause the video if you need more time.", start: 22.7, duration: 4.3 }
    ];
  };

  const handleUrlSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setVideoData(null);
    setCaptions([]);
    setCaptionsExtracted(false);

    if (!youtubeUrl.trim()) {
      setError('Please enter a YouTube URL');
      return;
    }

    const videoId = extractVideoId(youtubeUrl);
    if (!videoId) {
      setError('Please enter a valid YouTube URL');
      return;
    }

    setIsLoading(true);
    try {
      const data = await fetchVideoData(videoId);
      setVideoData(data);
    } catch (err) {
      setError('Failed to load video data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleExtractCaptions = async () => {
    if (!videoData) return;

    setIsExtractingCaptions(true);
    setError('');

    try {
      const captionData = await extractCaptions(videoData.id);
      setCaptions(captionData);
      setCaptionsExtracted(true);
    } catch (err) {
      setError('Failed to extract captions. Please try again.');
    } finally {
      setIsExtractingCaptions(false);
    }
  };

  const downloadCaptions = () => {
    if (captions.length === 0) return;

    const captionText = captions.map(caption => 
      `[${formatTime(caption.start)}] ${caption.text}`
    ).join('\n');

    const blob = new Blob([captionText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${videoData?.title || 'captions'}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const containerStyle = {
    maxWidth: '1200px',
    margin: '0 auto',
    padding: '0 20px'
  };

  const inputStyle = {
    width: '100%',
    padding: '16px 20px',
    border: '2px solid #e5e7eb',
    borderRadius: '12px',
    fontSize: '16px',
    outline: 'none',
    transition: 'border-color 0.2s ease'
  };

  const buttonStyle = {
    background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
    color: 'white',
    padding: '16px 32px',
    border: 'none',
    borderRadius: '12px',
    fontSize: '16px',
    fontWeight: '600',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
    display: 'flex',
    alignItems: 'center',
    gap: '8px'
  };

  return (
    <div style={{minHeight: '100vh', backgroundColor: '#f9fafb', paddingTop: '80px'}}>
      <div style={{...containerStyle, padding: '40px 20px'}}>
        {/* Header */}
        <div style={{textAlign: 'center', marginBottom: '40px'}}>
          <button
            onClick={onBack}
            style={{
              position: 'absolute',
              top: '100px',
              left: '20px',
              background: 'transparent',
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              padding: '8px 16px',
              cursor: 'pointer',
              color: '#6b7280'
            }}
          >
            ← Back to Dashboard
          </button>
          
          <h1 style={{fontSize: '36px', fontWeight: 'bold', color: '#1f2937', marginBottom: '16px'}}>
            YouTube Video Processor
          </h1>
          <p style={{fontSize: '18px', color: '#6b7280', maxWidth: '600px', margin: '0 auto'}}>
            Paste a YouTube URL below to extract captions and generate summaries
          </p>
        </div>

        {/* URL Input Form */}
        <div style={{
          backgroundColor: 'white',
          padding: '40px',
          borderRadius: '16px',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
          marginBottom: '40px'
        }}>
          <form onSubmit={handleUrlSubmit}>
            <div style={{display: 'flex', gap: '16px', alignItems: 'flex-end'}}>
              <div style={{flex: 1}}>
                <label style={{
                  display: 'block',
                  marginBottom: '8px',
                  color: '#374151',
                  fontWeight: '500',
                  fontSize: '16px'
                }}>
                  YouTube URL
                </label>
                <input
                  type="url"
                  value={youtubeUrl}
                  onChange={(e) => setYoutubeUrl(e.target.value)}
                  placeholder="https://www.youtube.com/watch?v=..."
                  style={inputStyle}
                  onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                  onBlur={(e) => e.target.style.borderColor = '#e5e7eb'}
                />
              </div>
              <button
                type="submit"
                disabled={isLoading}
                style={{
                  ...buttonStyle,
                  opacity: isLoading ? 0.7 : 1,
                  cursor: isLoading ? 'not-allowed' : 'pointer'
                }}
              >
                {isLoading ? (
                  <>
                    <ClockIcon style={{height: '20px', width: '20px'}} />
                    Loading...
                  </>
                ) : (
                  <>
                    <PlayIcon style={{height: '20px', width: '20px'}} />
                    Load Video
                  </>
                )}
              </button>
            </div>
          </form>

          {error && (
            <div style={{
              marginTop: '16px',
              padding: '12px 16px',
              backgroundColor: '#fef2f2',
              border: '1px solid #fecaca',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              <ExclamationTriangleIcon style={{height: '20px', width: '20px', color: '#ef4444'}} />
              <span style={{color: '#dc2626', fontSize: '14px'}}>{error}</span>
            </div>
          )}
        </div>

        {/* Video Display */}
        {videoData && (
          <div style={{
            backgroundColor: 'white',
            borderRadius: '16px',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
            overflow: 'hidden',
            marginBottom: '40px'
          }}>
            <div style={{padding: '24px'}}>
              <h2 style={{fontSize: '24px', fontWeight: 'bold', color: '#1f2937', marginBottom: '16px'}}>
                {videoData.title}
              </h2>
              <p style={{color: '#6b7280', marginBottom: '24px'}}>
                {videoData.channelName} • {videoData.duration}
              </p>
            </div>
            
            {/* Video Embed */}
            <div style={{position: 'relative', paddingBottom: '56.25%', height: 0}}>
              <iframe
                src={videoData.embedUrl}
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  border: 'none'
                }}
                allowFullScreen
                title={videoData.title}
              />
            </div>

            {/* Caption Controls */}
            <div style={{padding: '24px', borderTop: '1px solid #e5e7eb'}}>
              <div style={{display: 'flex', gap: '16px', alignItems: 'center'}}>
                <button
                  onClick={handleExtractCaptions}
                  disabled={isExtractingCaptions || captionsExtracted}
                  style={{
                    ...buttonStyle,
                    opacity: (isExtractingCaptions || captionsExtracted) ? 0.7 : 1,
                    cursor: (isExtractingCaptions || captionsExtracted) ? 'not-allowed' : 'pointer'
                  }}
                >
                  {isExtractingCaptions ? (
                    <>
                      <ClockIcon style={{height: '20px', width: '20px'}} />
                      Extracting...
                    </>
                  ) : captionsExtracted ? (
                    <>
                      <CheckCircleIcon style={{height: '20px', width: '20px'}} />
                      Captions Extracted
                    </>
                  ) : (
                    <>
                      <DocumentTextIcon style={{height: '20px', width: '20px'}} />
                      Extract Captions
                    </>
                  )}
                </button>

                {captionsExtracted && (
                  <button
                    onClick={downloadCaptions}
                    style={{
                      ...buttonStyle,
                      background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)'
                    }}
                  >
                    <CloudArrowDownIcon style={{height: '20px', width: '20px'}} />
                    Download Captions
                  </button>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Captions Display */}
        {captions.length > 0 && (
          <div style={{
            backgroundColor: 'white',
            padding: '32px',
            borderRadius: '16px',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
          }}>
            <h3 style={{fontSize: '20px', fontWeight: 'bold', color: '#1f2937', marginBottom: '24px'}}>
              Extracted Captions ({captions.length} segments)
            </h3>
            
            <div style={{
              maxHeight: '400px',
              overflowY: 'auto',
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              padding: '16px'
            }}>
              {captions.map((caption, index) => (
                <div key={index} style={{
                  marginBottom: '12px',
                  paddingBottom: '12px',
                  borderBottom: index < captions.length - 1 ? '1px solid #f3f4f6' : 'none'
                }}>
                  <div style={{
                    fontSize: '12px',
                    color: '#6b7280',
                    marginBottom: '4px',
                    fontFamily: 'monospace'
                  }}>
                    {formatTime(caption.start)}
                  </div>
                  <div style={{color: '#374151', lineHeight: '1.5'}}>
                    {caption.text}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default VideoProcessor;
