import React, { useState } from 'react';
import {
  PlayIcon,
  DocumentTextIcon,
  CloudArrowDownIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  EyeIcon
} from '@heroicons/react/24/outline';

interface VideoProcessorProps {
  onBack: () => void;
}

interface VideoInfo {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  duration: string;
  channelName: string;
  viewCount: string;
  embedUrl: string;
}

interface CaptionSegment {
  text: string;
  start: number;
  duration: number;
}

const VideoProcessor: React.FC<VideoProcessorProps> = ({ onBack }) => {
  const [youtubeUrl, setYoutubeUrl] = useState('');
  const [videoData, setVideoData] = useState<VideoInfo | null>(null);
  const [captions, setCaptions] = useState<CaptionSegment[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [isExtractingCaptions, setIsExtractingCaptions] = useState(false);
  const [captionsExtracted, setCaptionsExtracted] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  // Utility functions
  const extractVideoId = (url: string): string | null => {
    const patterns = [
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
      /youtube\.com\/watch\?.*v=([^&\n?#]+)/
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) return match[1];
    }
    return null;
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Get video info using oEmbed API
  const getVideoInfo = async (videoId: string): Promise<VideoInfo> => {
    try {
      const oembedUrl = `https://www.youtube.com/oembed?url=https://www.youtube.com/watch?v=${videoId}&format=json`;
      const response = await fetch(oembedUrl);

      if (!response.ok) {
        throw new Error('Video not found or unavailable');
      }

      const data = await response.json();

      return {
        id: videoId,
        title: data.title || 'Unknown Title',
        description: 'Video description will be available soon...',
        thumbnail: data.thumbnail_url || `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,
        duration: 'Unknown',
        channelName: data.author_name || 'Unknown Channel',
        viewCount: 'Unknown views',
        embedUrl: `https://www.youtube.com/embed/${videoId}`
      };
    } catch (error) {
      throw new Error('Failed to fetch video information. Please check the URL and try again.');
    }
  };

  // Real caption extraction from YouTube
  const extractCaptions = async (videoId: string): Promise<CaptionSegment[]> => {
    try {
      console.log('Attempting to extract real captions for:', videoId);

      // Method 1: Try to get captions from YouTube's timedtext API
      const captionUrl = `https://www.youtube.com/api/timedtext?v=${videoId}&lang=en&fmt=json3`;

      try {
        const response = await fetch(captionUrl, {
          mode: 'cors',
          headers: {
            'Accept': 'application/json',
          }
        });

        if (response.ok) {
          const data = await response.json();
          const captions = parseCaptionData(data);
          if (captions.length > 0) {
            console.log('Successfully extracted captions from timedtext API');
            return captions;
          }
        }
      } catch (e) {
        console.log('Timedtext API failed, trying alternative method...');
      }

      // Method 2: Try to extract from video page
      const pageUrl = `https://www.youtube.com/watch?v=${videoId}`;

      try {
        // Use a CORS proxy for this request
        const proxyUrl = `https://api.allorigins.win/get?url=${encodeURIComponent(pageUrl)}`;
        const response = await fetch(proxyUrl);

        if (response.ok) {
          const data = await response.json();
          const captions = await extractCaptionsFromPage(data.contents, videoId);
          if (captions.length > 0) {
            console.log('Successfully extracted captions from video page');
            return captions;
          }
        }
      } catch (e) {
        console.log('Page extraction failed, trying direct subtitle URL...');
      }

      // Method 3: Try common subtitle URLs
      const subtitleUrls = [
        `https://www.youtube.com/api/timedtext?v=${videoId}&lang=en&fmt=srv3`,
        `https://www.youtube.com/api/timedtext?v=${videoId}&lang=en-US&fmt=srv3`,
        `https://www.youtube.com/api/timedtext?v=${videoId}&lang=en&fmt=vtt`,
        `https://www.youtube.com/api/timedtext?v=${videoId}&lang=en-US&fmt=vtt`
      ];

      for (const url of subtitleUrls) {
        try {
          const response = await fetch(url);
          if (response.ok) {
            const text = await response.text();
            const captions = parseSubtitleText(text);
            if (captions.length > 0) {
              console.log('Successfully extracted captions from subtitle URL');
              return captions;
            }
          }
        } catch (e) {
          continue;
        }
      }

      throw new Error('No captions found');

    } catch (error) {
      console.error('All caption extraction methods failed:', error);
      throw new Error('No subtitles available for this video. The video may not have closed captions enabled.');
    }
  };

  // Parse caption data from YouTube's JSON format
  const parseCaptionData = (data: any): CaptionSegment[] => {
    try {
      if (data.events) {
        return data.events
          .filter((event: any) => event.segs && event.segs.length > 0)
          .map((event: any) => ({
            text: event.segs.map((seg: any) => seg.utf8 || '').join('').trim(),
            start: event.tStartMs ? event.tStartMs / 1000 : 0,
            duration: event.dDurationMs ? event.dDurationMs / 1000 : 3
          }))
          .filter((caption: CaptionSegment) => caption.text.length > 0);
      }
      return [];
    } catch (error) {
      console.error('Error parsing caption data:', error);
      return [];
    }
  };

  // Extract captions from video page HTML
  const extractCaptionsFromPage = async (html: string, videoId: string): Promise<CaptionSegment[]> => {
    try {
      // Look for caption tracks in the page
      const captionRegex = /"captionTracks":\s*(\[.*?\])/;
      const match = html.match(captionRegex);

      if (match) {
        const captionTracks = JSON.parse(match[1]);
        const englishTrack = captionTracks.find((track: any) =>
          track.languageCode === 'en' || track.languageCode.startsWith('en')
        );

        if (englishTrack && englishTrack.baseUrl) {
          const captionResponse = await fetch(englishTrack.baseUrl);
          const captionXml = await captionResponse.text();
          return parseXmlCaptions(captionXml);
        }
      }

      return [];
    } catch (error) {
      console.error('Error extracting captions from page:', error);
      return [];
    }
  };

  // Parse XML caption format
  const parseXmlCaptions = (xml: string): CaptionSegment[] => {
    try {
      const parser = new DOMParser();
      const doc = parser.parseFromString(xml, 'text/xml');
      const textElements = doc.querySelectorAll('text');

      return Array.from(textElements).map((element) => ({
        text: element.textContent?.replace(/&amp;/g, '&').replace(/&lt;/g, '<').replace(/&gt;/g, '>') || '',
        start: parseFloat(element.getAttribute('start') || '0'),
        duration: parseFloat(element.getAttribute('dur') || '3')
      })).filter(caption => caption.text.trim().length > 0);
    } catch (error) {
      console.error('Error parsing XML captions:', error);
      return [];
    }
  };

  // Parse subtitle text formats (VTT, SRT, etc.)
  const parseSubtitleText = (text: string): CaptionSegment[] => {
    try {
      const captions: CaptionSegment[] = [];

      // Handle WebVTT format
      if (text.includes('WEBVTT')) {
        const lines = text.split('\n');
        let currentCaption: Partial<CaptionSegment> = {};

        for (let i = 0; i < lines.length; i++) {
          const line = lines[i].trim();

          // Time format: 00:00:00.000 --> 00:00:03.000
          if (line.includes('-->')) {
            const [startTime, endTime] = line.split('-->').map(t => t.trim());
            const start = parseTimeString(startTime);
            const end = parseTimeString(endTime);
            currentCaption = {
              start: start,
              duration: end - start,
              text: ''
            };
          } else if (line && !line.includes('WEBVTT') && !line.includes('NOTE') && currentCaption.start !== undefined) {
            currentCaption.text = (currentCaption.text || '') + line + ' ';
          } else if (!line && currentCaption.text) {
            captions.push(currentCaption as CaptionSegment);
            currentCaption = {};
          }
        }

        if (currentCaption.text) {
          captions.push(currentCaption as CaptionSegment);
        }
      }

      return captions.filter(caption => caption.text.trim().length > 0);
    } catch (error) {
      console.error('Error parsing subtitle text:', error);
      return [];
    }
  };

  // Parse time string to seconds
  const parseTimeString = (timeStr: string): number => {
    try {
      const parts = timeStr.split(':');
      const seconds = parseFloat(parts[parts.length - 1]);
      const minutes = parseInt(parts[parts.length - 2] || '0');
      const hours = parseInt(parts[parts.length - 3] || '0');

      return hours * 3600 + minutes * 60 + seconds;
    } catch (error) {
      return 0;
    }
  };

  const handleUrlSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setVideoData(null);
    setCaptions([]);
    setCaptionsExtracted(false);

    if (!youtubeUrl.trim()) {
      setError('Please enter a YouTube URL');
      return;
    }

    const videoId = extractVideoId(youtubeUrl);
    if (!videoId) {
      setError('Please enter a valid YouTube URL');
      return;
    }

    setIsLoading(true);
    try {
      console.log('Fetching video info for:', videoId);
      const data = await getVideoInfo(videoId);
      setVideoData(data);
      console.log('Video info loaded:', data);
    } catch (err: any) {
      console.error('Error loading video:', err);
      setError(err.message || 'Failed to load video data. Please check the URL and try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleExtractCaptions = async () => {
    if (!videoData) return;

    setIsExtractingCaptions(true);
    setError('');
    setSuccessMessage('');

    try {
      console.log('Extracting captions for video:', videoData.id);
      const captionData = await extractCaptions(videoData.id);

      if (captionData.length === 0) {
        throw new Error('No captions found for this video. The video may not have subtitles enabled.');
      }

      setCaptions(captionData);
      setCaptionsExtracted(true);
      console.log(`Successfully extracted ${captionData.length} caption segments`);

      // Show success message
      setSuccessMessage(`Successfully extracted ${captionData.length} caption segments!`);
      setError(''); // Clear any previous errors

      // Clear success message after 5 seconds
      setTimeout(() => setSuccessMessage(''), 5000);

    } catch (err: any) {
      console.error('Error extracting captions:', err);
      setError(err.message || 'Failed to extract captions. This video may not have closed captions available.');
      setCaptions([]);
      setCaptionsExtracted(false);
    } finally {
      setIsExtractingCaptions(false);
    }
  };

  const downloadCaptions = () => {
    if (captions.length === 0 || !videoData) return;

    // Create detailed caption file
    const header = `Video: ${videoData.title}\nChannel: ${videoData.channelName}\nDuration: ${videoData.duration}\nExtracted: ${new Date().toLocaleString()}\n\n--- CAPTIONS ---\n\n`;

    const captionText = captions.map(caption =>
      `[${formatTime(caption.start)}] ${caption.text}`
    ).join('\n');

    const fullTranscript = captions.map(caption => caption.text).join(' ');
    const fullText = header + captionText + '\n\n--- FULL TRANSCRIPT ---\n\n' + fullTranscript;

    const blob = new Blob([fullText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${videoData.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_captions.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };



  const containerStyle = {
    maxWidth: '1200px',
    margin: '0 auto',
    padding: '0 20px'
  };

  const inputStyle = {
    width: '100%',
    padding: '16px 20px',
    border: '2px solid #e5e7eb',
    borderRadius: '12px',
    fontSize: '16px',
    outline: 'none',
    transition: 'border-color 0.2s ease'
  };

  const buttonStyle = {
    background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
    color: 'white',
    padding: '16px 32px',
    border: 'none',
    borderRadius: '12px',
    fontSize: '16px',
    fontWeight: '600',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
    display: 'flex',
    alignItems: 'center',
    gap: '8px'
  };

  return (
    <div style={{minHeight: '100vh', backgroundColor: '#f9fafb', paddingTop: '80px'}}>
      <div style={{...containerStyle, padding: '40px 20px'}}>
        {/* Header */}
        <div style={{textAlign: 'center', marginBottom: '40px'}}>
          <button
            onClick={onBack}
            style={{
              position: 'absolute',
              top: '100px',
              left: '20px',
              background: 'transparent',
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              padding: '8px 16px',
              cursor: 'pointer',
              color: '#6b7280'
            }}
          >
            ← Back to Dashboard
          </button>
          
          <h1 style={{fontSize: '36px', fontWeight: 'bold', color: '#1f2937', marginBottom: '16px'}}>
            YouTube Video Processor
          </h1>
          <p style={{fontSize: '18px', color: '#6b7280', maxWidth: '600px', margin: '0 auto'}}>
            Paste a YouTube URL below to extract captions and generate summaries
          </p>
        </div>

        {/* URL Input Form */}
        <div style={{
          backgroundColor: 'white',
          padding: '40px',
          borderRadius: '16px',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
          marginBottom: '40px'
        }}>
          <form onSubmit={handleUrlSubmit}>
            <div style={{display: 'flex', gap: '16px', alignItems: 'flex-end'}}>
              <div style={{flex: 1}}>
                <label style={{
                  display: 'block',
                  marginBottom: '8px',
                  color: '#374151',
                  fontWeight: '500',
                  fontSize: '16px'
                }}>
                  YouTube URL
                </label>
                <input
                  type="url"
                  value={youtubeUrl}
                  onChange={(e) => setYoutubeUrl(e.target.value)}
                  placeholder="https://www.youtube.com/watch?v=..."
                  style={inputStyle}
                  onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                  onBlur={(e) => e.target.style.borderColor = '#e5e7eb'}
                />
              </div>
              <button
                type="submit"
                disabled={isLoading}
                style={{
                  ...buttonStyle,
                  opacity: isLoading ? 0.7 : 1,
                  cursor: isLoading ? 'not-allowed' : 'pointer'
                }}
              >
                {isLoading ? (
                  <>
                    <ClockIcon style={{height: '20px', width: '20px'}} />
                    Loading...
                  </>
                ) : (
                  <>
                    <PlayIcon style={{height: '20px', width: '20px'}} />
                    Load Video
                  </>
                )}
              </button>
            </div>
          </form>

          {error && (
            <div style={{
              marginTop: '16px',
              padding: '12px 16px',
              backgroundColor: '#fef2f2',
              border: '1px solid #fecaca',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              <ExclamationTriangleIcon style={{height: '20px', width: '20px', color: '#ef4444'}} />
              <span style={{color: '#dc2626', fontSize: '14px'}}>{error}</span>
            </div>
          )}

          {successMessage && (
            <div style={{
              marginTop: '16px',
              padding: '12px 16px',
              backgroundColor: '#f0fdf4',
              border: '1px solid #bbf7d0',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              <CheckCircleIcon style={{height: '20px', width: '20px', color: '#22c55e'}} />
              <span style={{color: '#15803d', fontSize: '14px'}}>{successMessage}</span>
            </div>
          )}
        </div>

        {/* Video Display */}
        {videoData && (
          <div style={{
            backgroundColor: 'white',
            borderRadius: '16px',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
            overflow: 'hidden',
            marginBottom: '40px'
          }}>
            <div style={{padding: '24px'}}>
              <h2 style={{fontSize: '24px', fontWeight: 'bold', color: '#1f2937', marginBottom: '16px'}}>
                {videoData.title}
              </h2>

              {/* Video metadata */}
              <div style={{display: 'flex', flexWrap: 'wrap', gap: '16px', marginBottom: '16px'}}>
                <div style={{display: 'flex', alignItems: 'center', gap: '6px', color: '#6b7280'}}>
                  <span style={{fontWeight: '500'}}>Channel:</span>
                  <span>{videoData.channelName}</span>
                </div>
                <div style={{display: 'flex', alignItems: 'center', gap: '6px', color: '#6b7280'}}>
                  <ClockIcon style={{height: '16px', width: '16px'}} />
                  <span>{videoData.duration}</span>
                </div>
                {videoData.viewCount && (
                  <div style={{display: 'flex', alignItems: 'center', gap: '6px', color: '#6b7280'}}>
                    <EyeIcon style={{height: '16px', width: '16px'}} />
                    <span>{videoData.viewCount}</span>
                  </div>
                )}
              </div>

              {/* Description preview */}
              {videoData.description && (
                <div style={{
                  backgroundColor: '#f9fafb',
                  padding: '12px',
                  borderRadius: '8px',
                  marginBottom: '16px'
                }}>
                  <p style={{
                    color: '#374151',
                    fontSize: '14px',
                    lineHeight: '1.5',
                    margin: 0,
                    maxHeight: '60px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                  }}>
                    {videoData.description.length > 200
                      ? videoData.description.substring(0, 200) + '...'
                      : videoData.description
                    }
                  </p>
                </div>
              )}
            </div>
            
            {/* Video Embed */}
            <div style={{position: 'relative', paddingBottom: '56.25%', height: 0}}>
              <iframe
                src={videoData.embedUrl}
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  border: 'none'
                }}
                allowFullScreen
                title={videoData.title}
              />
            </div>

            {/* Caption Controls */}
            <div style={{padding: '24px', borderTop: '1px solid #e5e7eb'}}>
              <div style={{display: 'flex', gap: '16px', alignItems: 'center'}}>
                <button
                  onClick={handleExtractCaptions}
                  disabled={isExtractingCaptions || captionsExtracted}
                  style={{
                    ...buttonStyle,
                    opacity: (isExtractingCaptions || captionsExtracted) ? 0.7 : 1,
                    cursor: (isExtractingCaptions || captionsExtracted) ? 'not-allowed' : 'pointer'
                  }}
                >
                  {isExtractingCaptions ? (
                    <>
                      <ClockIcon style={{height: '20px', width: '20px'}} />
                      Extracting...
                    </>
                  ) : captionsExtracted ? (
                    <>
                      <CheckCircleIcon style={{height: '20px', width: '20px'}} />
                      Captions Extracted
                    </>
                  ) : (
                    <>
                      <DocumentTextIcon style={{height: '20px', width: '20px'}} />
                      Extract Captions
                    </>
                  )}
                </button>

                {captionsExtracted && (
                  <button
                    onClick={downloadCaptions}
                    style={{
                      ...buttonStyle,
                      background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)'
                    }}
                  >
                    <CloudArrowDownIcon style={{height: '20px', width: '20px'}} />
                    Download Captions
                  </button>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Captions Display */}
        {captions.length > 0 && (
          <div style={{
            backgroundColor: 'white',
            padding: '32px',
            borderRadius: '16px',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
          }}>
            <h3 style={{fontSize: '20px', fontWeight: 'bold', color: '#1f2937', marginBottom: '24px'}}>
              Extracted Captions ({captions.length} segments)
            </h3>
            
            <div style={{
              maxHeight: '400px',
              overflowY: 'auto',
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              padding: '16px'
            }}>
              {captions.map((caption, index) => (
                <div key={index} style={{
                  marginBottom: '12px',
                  paddingBottom: '12px',
                  borderBottom: index < captions.length - 1 ? '1px solid #f3f4f6' : 'none'
                }}>
                  <div style={{
                    fontSize: '12px',
                    color: '#6b7280',
                    marginBottom: '4px',
                    fontFamily: 'monospace'
                  }}>
                    {formatTime(caption.start)}
                  </div>
                  <div style={{color: '#374151', lineHeight: '1.5'}}>
                    {caption.text}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default VideoProcessor;
