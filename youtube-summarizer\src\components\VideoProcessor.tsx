import React, { useState } from 'react';
import {
  PlayIcon,
  DocumentTextIcon,
  CloudArrowDownIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  EyeIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';
import { YouTubeService, VideoInfo, CaptionSegment } from '../services/youtubeService';

interface VideoProcessorProps {
  onBack: () => void;
}

// Using types from YouTubeService

const VideoProcessor: React.FC<VideoProcessorProps> = ({ onBack }) => {
  const [youtubeUrl, setYoutubeUrl] = useState('');
  const [videoData, setVideoData] = useState<VideoInfo | null>(null);
  const [captions, setCaptions] = useState<CaptionSegment[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [isExtractingCaptions, setIsExtractingCaptions] = useState(false);
  const [captionsExtracted, setCaptionsExtracted] = useState(false);
  const [fullTranscript, setFullTranscript] = useState('');

  // Real implementation using YouTubeService

  const handleUrlSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setVideoData(null);
    setCaptions([]);
    setCaptionsExtracted(false);
    setFullTranscript('');

    if (!youtubeUrl.trim()) {
      setError('Please enter a YouTube URL');
      return;
    }

    // Validate YouTube URL
    if (!YouTubeService.isValidYouTubeUrl(youtubeUrl)) {
      setError('Please enter a valid YouTube URL');
      return;
    }

    const videoId = YouTubeService.extractVideoId(youtubeUrl);
    if (!videoId) {
      setError('Could not extract video ID from URL');
      return;
    }

    setIsLoading(true);
    try {
      console.log('Fetching video info for:', videoId);
      const data = await YouTubeService.getVideoInfo(videoId);
      setVideoData(data);
      console.log('Video info loaded:', data);
    } catch (err: any) {
      console.error('Error loading video:', err);
      setError(err.message || 'Failed to load video data. Please check the URL and try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleExtractCaptions = async () => {
    if (!videoData) return;

    setIsExtractingCaptions(true);
    setError('');

    try {
      console.log('Extracting captions for video:', videoData.id);
      const captionData = await YouTubeService.getCaptions(videoData.id);
      setCaptions(captionData);
      setCaptionsExtracted(true);

      // Also get full transcript
      const transcript = await YouTubeService.getFullTranscript(videoData.id);
      setFullTranscript(transcript);

      console.log(`Successfully extracted ${captionData.length} caption segments`);
    } catch (err: any) {
      console.error('Error extracting captions:', err);
      setError(err.message || 'Failed to extract captions. This video may not have closed captions available.');
    } finally {
      setIsExtractingCaptions(false);
    }
  };

  const downloadCaptions = () => {
    if (captions.length === 0 || !videoData) return;

    // Create detailed caption file
    const header = `Video: ${videoData.title}\nChannel: ${videoData.channelName}\nDuration: ${videoData.duration}\nExtracted: ${new Date().toLocaleString()}\n\n--- CAPTIONS ---\n\n`;

    const captionText = captions.map(caption =>
      `[${YouTubeService.formatTime(caption.start)}] ${caption.text}`
    ).join('\n');

    const fullText = header + captionText + '\n\n--- FULL TRANSCRIPT ---\n\n' + fullTranscript;

    const blob = new Blob([fullText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${videoData.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_captions.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };



  const containerStyle = {
    maxWidth: '1200px',
    margin: '0 auto',
    padding: '0 20px'
  };

  const inputStyle = {
    width: '100%',
    padding: '16px 20px',
    border: '2px solid #e5e7eb',
    borderRadius: '12px',
    fontSize: '16px',
    outline: 'none',
    transition: 'border-color 0.2s ease'
  };

  const buttonStyle = {
    background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
    color: 'white',
    padding: '16px 32px',
    border: 'none',
    borderRadius: '12px',
    fontSize: '16px',
    fontWeight: '600',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
    display: 'flex',
    alignItems: 'center',
    gap: '8px'
  };

  return (
    <div style={{minHeight: '100vh', backgroundColor: '#f9fafb', paddingTop: '80px'}}>
      <div style={{...containerStyle, padding: '40px 20px'}}>
        {/* Header */}
        <div style={{textAlign: 'center', marginBottom: '40px'}}>
          <button
            onClick={onBack}
            style={{
              position: 'absolute',
              top: '100px',
              left: '20px',
              background: 'transparent',
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              padding: '8px 16px',
              cursor: 'pointer',
              color: '#6b7280'
            }}
          >
            ← Back to Dashboard
          </button>
          
          <h1 style={{fontSize: '36px', fontWeight: 'bold', color: '#1f2937', marginBottom: '16px'}}>
            YouTube Video Processor
          </h1>
          <p style={{fontSize: '18px', color: '#6b7280', maxWidth: '600px', margin: '0 auto'}}>
            Paste a YouTube URL below to extract captions and generate summaries
          </p>
        </div>

        {/* URL Input Form */}
        <div style={{
          backgroundColor: 'white',
          padding: '40px',
          borderRadius: '16px',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
          marginBottom: '40px'
        }}>
          <form onSubmit={handleUrlSubmit}>
            <div style={{display: 'flex', gap: '16px', alignItems: 'flex-end'}}>
              <div style={{flex: 1}}>
                <label style={{
                  display: 'block',
                  marginBottom: '8px',
                  color: '#374151',
                  fontWeight: '500',
                  fontSize: '16px'
                }}>
                  YouTube URL
                </label>
                <input
                  type="url"
                  value={youtubeUrl}
                  onChange={(e) => setYoutubeUrl(e.target.value)}
                  placeholder="https://www.youtube.com/watch?v=..."
                  style={inputStyle}
                  onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                  onBlur={(e) => e.target.style.borderColor = '#e5e7eb'}
                />
              </div>
              <button
                type="submit"
                disabled={isLoading}
                style={{
                  ...buttonStyle,
                  opacity: isLoading ? 0.7 : 1,
                  cursor: isLoading ? 'not-allowed' : 'pointer'
                }}
              >
                {isLoading ? (
                  <>
                    <ClockIcon style={{height: '20px', width: '20px'}} />
                    Loading...
                  </>
                ) : (
                  <>
                    <PlayIcon style={{height: '20px', width: '20px'}} />
                    Load Video
                  </>
                )}
              </button>
            </div>
          </form>

          {error && (
            <div style={{
              marginTop: '16px',
              padding: '12px 16px',
              backgroundColor: '#fef2f2',
              border: '1px solid #fecaca',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              <ExclamationTriangleIcon style={{height: '20px', width: '20px', color: '#ef4444'}} />
              <span style={{color: '#dc2626', fontSize: '14px'}}>{error}</span>
            </div>
          )}
        </div>

        {/* Video Display */}
        {videoData && (
          <div style={{
            backgroundColor: 'white',
            borderRadius: '16px',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
            overflow: 'hidden',
            marginBottom: '40px'
          }}>
            <div style={{padding: '24px'}}>
              <h2 style={{fontSize: '24px', fontWeight: 'bold', color: '#1f2937', marginBottom: '16px'}}>
                {videoData.title}
              </h2>

              {/* Video metadata */}
              <div style={{display: 'flex', flexWrap: 'wrap', gap: '16px', marginBottom: '16px'}}>
                <div style={{display: 'flex', alignItems: 'center', gap: '6px', color: '#6b7280'}}>
                  <span style={{fontWeight: '500'}}>Channel:</span>
                  <span>{videoData.channelName}</span>
                </div>
                <div style={{display: 'flex', alignItems: 'center', gap: '6px', color: '#6b7280'}}>
                  <ClockIcon style={{height: '16px', width: '16px'}} />
                  <span>{videoData.duration}</span>
                </div>
                {videoData.viewCount && (
                  <div style={{display: 'flex', alignItems: 'center', gap: '6px', color: '#6b7280'}}>
                    <EyeIcon style={{height: '16px', width: '16px'}} />
                    <span>{videoData.viewCount}</span>
                  </div>
                )}
              </div>

              {/* Description preview */}
              {videoData.description && (
                <div style={{
                  backgroundColor: '#f9fafb',
                  padding: '12px',
                  borderRadius: '8px',
                  marginBottom: '16px'
                }}>
                  <p style={{
                    color: '#374151',
                    fontSize: '14px',
                    lineHeight: '1.5',
                    margin: 0,
                    maxHeight: '60px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                  }}>
                    {videoData.description.length > 200
                      ? videoData.description.substring(0, 200) + '...'
                      : videoData.description
                    }
                  </p>
                </div>
              )}
            </div>
            
            {/* Video Embed */}
            <div style={{position: 'relative', paddingBottom: '56.25%', height: 0}}>
              <iframe
                src={videoData.embedUrl}
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  border: 'none'
                }}
                allowFullScreen
                title={videoData.title}
              />
            </div>

            {/* Caption Controls */}
            <div style={{padding: '24px', borderTop: '1px solid #e5e7eb'}}>
              <div style={{display: 'flex', gap: '16px', alignItems: 'center'}}>
                <button
                  onClick={handleExtractCaptions}
                  disabled={isExtractingCaptions || captionsExtracted}
                  style={{
                    ...buttonStyle,
                    opacity: (isExtractingCaptions || captionsExtracted) ? 0.7 : 1,
                    cursor: (isExtractingCaptions || captionsExtracted) ? 'not-allowed' : 'pointer'
                  }}
                >
                  {isExtractingCaptions ? (
                    <>
                      <ClockIcon style={{height: '20px', width: '20px'}} />
                      Extracting...
                    </>
                  ) : captionsExtracted ? (
                    <>
                      <CheckCircleIcon style={{height: '20px', width: '20px'}} />
                      Captions Extracted
                    </>
                  ) : (
                    <>
                      <DocumentTextIcon style={{height: '20px', width: '20px'}} />
                      Extract Captions
                    </>
                  )}
                </button>

                {captionsExtracted && (
                  <button
                    onClick={downloadCaptions}
                    style={{
                      ...buttonStyle,
                      background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)'
                    }}
                  >
                    <CloudArrowDownIcon style={{height: '20px', width: '20px'}} />
                    Download Captions
                  </button>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Captions Display */}
        {captions.length > 0 && (
          <div style={{
            backgroundColor: 'white',
            padding: '32px',
            borderRadius: '16px',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
          }}>
            <h3 style={{fontSize: '20px', fontWeight: 'bold', color: '#1f2937', marginBottom: '24px'}}>
              Extracted Captions ({captions.length} segments)
            </h3>
            
            <div style={{
              maxHeight: '400px',
              overflowY: 'auto',
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              padding: '16px'
            }}>
              {captions.map((caption, index) => (
                <div key={index} style={{
                  marginBottom: '12px',
                  paddingBottom: '12px',
                  borderBottom: index < captions.length - 1 ? '1px solid #f3f4f6' : 'none'
                }}>
                  <div style={{
                    fontSize: '12px',
                    color: '#6b7280',
                    marginBottom: '4px',
                    fontFamily: 'monospace'
                  }}>
                    {YouTubeService.formatTime(caption.start)}
                  </div>
                  <div style={{color: '#374151', lineHeight: '1.5'}}>
                    {caption.text}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default VideoProcessor;
