import React from 'react';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

interface LoginNavigationProps {
  onBackToHome: () => void;
}

const LoginNavigation: React.FC<LoginNavigationProps> = ({ onBackToHome }) => {
  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-md border-b border-gray-200/30 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <div className="flex items-center space-x-2 cursor-pointer" onClick={onBackToHome}>
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">📹</span>
              </div>
              <span className="text-xl font-bold text-gray-900">VidSum</span>
            </div>
          </div>

          {/* Back to Home Button */}
          <button
            onClick={onBackToHome}
            className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 px-4 py-2 rounded-lg hover:bg-gray-50 transition-all duration-200"
          >
            <ArrowLeftIcon className="h-4 w-4" />
            <span className="text-sm font-medium">Back to Home</span>
          </button>
        </div>
      </div>
    </nav>
  );
};

export default LoginNavigation;
