import React from 'react';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

interface LoginNavigationProps {
  onBackToHome: () => void;
}

const LoginNavigation: React.FC<LoginNavigationProps> = ({ onBackToHome }) => {
  const navStyle = {
    position: 'fixed' as const,
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    backdropFilter: 'blur(12px)',
    borderBottom: '1px solid rgba(229, 231, 235, 0.3)',
    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
  };

  const containerStyle = {
    maxWidth: '1280px',
    margin: '0 auto',
    padding: '0 16px'
  };

  const flexStyle = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: '64px'
  };

  const logoStyle = {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    cursor: 'pointer'
  };

  const logoIconStyle = {
    width: '32px',
    height: '32px',
    background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
    borderRadius: '8px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  };

  return (
    <nav style={navStyle}>
      <div style={containerStyle}>
        <div style={flexStyle}>
          {/* Logo */}
          <div style={logoStyle} onClick={onBackToHome}>
            <div style={logoIconStyle}>
              <span style={{color: 'white', fontWeight: 'bold', fontSize: '18px'}}>📹</span>
            </div>
            <span style={{fontSize: '20px', fontWeight: 'bold', color: '#111827'}}>VidSum</span>
          </div>

          {/* Back to Home Button */}
          <button
            onClick={onBackToHome}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              color: '#6b7280',
              padding: '8px 16px',
              borderRadius: '8px',
              border: 'none',
              background: 'transparent',
              cursor: 'pointer',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.color = '#2563eb';
              e.currentTarget.style.backgroundColor = '#f9fafb';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.color = '#6b7280';
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
          >
            <ArrowLeftIcon style={{height: '16px', width: '16px'}} />
            <span style={{fontSize: '14px', fontWeight: '500'}}>Back to Home</span>
          </button>
        </div>
      </div>
    </nav>
  );
};

export default LoginNavigation;
