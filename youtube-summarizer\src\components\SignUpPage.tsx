import React, { useState } from 'react';
import { EyeIcon, EyeSlashIcon, CheckIcon } from '@heroicons/react/24/outline';
import LoginNavigation from './LoginNavigation';

interface SignUpPageProps {
  onBackToHome: () => void;
  onSignUpSuccess: () => void;
  onSwitchToLogin: () => void;
}

const SignUpPage: React.FC<SignUpPageProps> = ({ onBackToHome, onSignUpSuccess, onSwitchToLogin }) => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  const containerStyle = {
    maxWidth: '1200px',
    margin: '0 auto',
    padding: '0 20px'
  };

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!formData.firstName.trim()) newErrors.firstName = 'First name is required';
    if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required';
    if (!formData.email.trim()) newErrors.email = 'Email is required';
    else if (!/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = 'Email is invalid';
    if (!formData.password) newErrors.password = 'Password is required';
    else if (formData.password.length < 8) newErrors.password = 'Password must be at least 8 characters';
    if (formData.password !== formData.confirmPassword) newErrors.confirmPassword = 'Passwords do not match';
    if (!agreedToTerms) newErrors.terms = 'You must agree to the terms and conditions';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      // Here you would typically make an API call to create the account
      console.log('Sign up data:', formData);
      onSignUpSuccess();
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const inputStyle = {
    width: '100%',
    padding: '12px 16px',
    border: '2px solid #e5e7eb',
    borderRadius: '8px',
    fontSize: '16px',
    transition: 'border-color 0.2s ease',
    outline: 'none'
  };

  const inputErrorStyle = {
    ...inputStyle,
    borderColor: '#ef4444'
  };

  const inputFocusStyle = {
    borderColor: '#3b82f6'
  };

  const buttonStyle = {
    width: '100%',
    background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
    color: 'white',
    padding: '12px 24px',
    border: 'none',
    borderRadius: '8px',
    fontSize: '16px',
    fontWeight: '600',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
  };

  return (
    <div style={{minHeight: '100vh', backgroundColor: '#f9fafb'}}>
      <LoginNavigation onBackToHome={onBackToHome} />
      <div style={{paddingTop: '64px'}}>
        <div style={{...containerStyle, padding: '40px 20px'}}>
          <div style={{
            maxWidth: '500px', 
            margin: '0 auto', 
            backgroundColor: 'white', 
            padding: '40px', 
            borderRadius: '12px', 
            boxShadow: '0 4px 6px rgba(0,0,0,0.1)'
          }}>
            <div style={{textAlign: 'center', marginBottom: '30px'}}>
              <h2 style={{fontSize: '28px', marginBottom: '8px', color: '#1f2937', fontWeight: 'bold'}}>
                Create Your Account
              </h2>
              <p style={{color: '#6b7280', fontSize: '16px'}}>
                Join VidSum and start summarizing videos instantly
              </p>
            </div>

            <form onSubmit={handleSubmit}>
              {/* Name Fields */}
              <div style={{display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '20px'}}>
                <div>
                  <label style={{display: 'block', marginBottom: '6px', color: '#374151', fontWeight: '500'}}>
                    First Name
                  </label>
                  <input
                    type="text"
                    value={formData.firstName}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                    style={errors.firstName ? inputErrorStyle : inputStyle}
                    onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                    onBlur={(e) => e.target.style.borderColor = errors.firstName ? '#ef4444' : '#e5e7eb'}
                    placeholder="John"
                  />
                  {errors.firstName && (
                    <p style={{color: '#ef4444', fontSize: '14px', marginTop: '4px'}}>{errors.firstName}</p>
                  )}
                </div>
                <div>
                  <label style={{display: 'block', marginBottom: '6px', color: '#374151', fontWeight: '500'}}>
                    Last Name
                  </label>
                  <input
                    type="text"
                    value={formData.lastName}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                    style={errors.lastName ? inputErrorStyle : inputStyle}
                    onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                    onBlur={(e) => e.target.style.borderColor = errors.lastName ? '#ef4444' : '#e5e7eb'}
                    placeholder="Doe"
                  />
                  {errors.lastName && (
                    <p style={{color: '#ef4444', fontSize: '14px', marginTop: '4px'}}>{errors.lastName}</p>
                  )}
                </div>
              </div>

              {/* Email Field */}
              <div style={{marginBottom: '20px'}}>
                <label style={{display: 'block', marginBottom: '6px', color: '#374151', fontWeight: '500'}}>
                  Email Address
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  style={errors.email ? inputErrorStyle : inputStyle}
                  onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                  onBlur={(e) => e.target.style.borderColor = errors.email ? '#ef4444' : '#e5e7eb'}
                  placeholder="<EMAIL>"
                />
                {errors.email && (
                  <p style={{color: '#ef4444', fontSize: '14px', marginTop: '4px'}}>{errors.email}</p>
                )}
              </div>

              {/* Password Field */}
              <div style={{marginBottom: '20px'}}>
                <label style={{display: 'block', marginBottom: '6px', color: '#374151', fontWeight: '500'}}>
                  Password
                </label>
                <div style={{position: 'relative'}}>
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={(e) => handleInputChange('password', e.target.value)}
                    style={errors.password ? inputErrorStyle : inputStyle}
                    onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                    onBlur={(e) => e.target.style.borderColor = errors.password ? '#ef4444' : '#e5e7eb'}
                    placeholder="Enter your password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    style={{
                      position: 'absolute',
                      right: '12px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      border: 'none',
                      background: 'transparent',
                      cursor: 'pointer',
                      color: '#6b7280'
                    }}
                  >
                    {showPassword ? (
                      <EyeSlashIcon style={{height: '20px', width: '20px'}} />
                    ) : (
                      <EyeIcon style={{height: '20px', width: '20px'}} />
                    )}
                  </button>
                </div>
                {errors.password && (
                  <p style={{color: '#ef4444', fontSize: '14px', marginTop: '4px'}}>{errors.password}</p>
                )}
              </div>

              {/* Confirm Password Field */}
              <div style={{marginBottom: '20px'}}>
                <label style={{display: 'block', marginBottom: '6px', color: '#374151', fontWeight: '500'}}>
                  Confirm Password
                </label>
                <div style={{position: 'relative'}}>
                  <input
                    type={showConfirmPassword ? 'text' : 'password'}
                    value={formData.confirmPassword}
                    onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                    style={errors.confirmPassword ? inputErrorStyle : inputStyle}
                    onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                    onBlur={(e) => e.target.style.borderColor = errors.confirmPassword ? '#ef4444' : '#e5e7eb'}
                    placeholder="Confirm your password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    style={{
                      position: 'absolute',
                      right: '12px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      border: 'none',
                      background: 'transparent',
                      cursor: 'pointer',
                      color: '#6b7280'
                    }}
                  >
                    {showConfirmPassword ? (
                      <EyeSlashIcon style={{height: '20px', width: '20px'}} />
                    ) : (
                      <EyeIcon style={{height: '20px', width: '20px'}} />
                    )}
                  </button>
                </div>
                {errors.confirmPassword && (
                  <p style={{color: '#ef4444', fontSize: '14px', marginTop: '4px'}}>{errors.confirmPassword}</p>
                )}
              </div>

              {/* Terms and Conditions */}
              <div style={{marginBottom: '30px'}}>
                <label style={{display: 'flex', alignItems: 'flex-start', cursor: 'pointer'}}>
                  <div style={{position: 'relative', marginRight: '12px', marginTop: '2px'}}>
                    <input
                      type="checkbox"
                      checked={agreedToTerms}
                      onChange={(e) => setAgreedToTerms(e.target.checked)}
                      style={{opacity: 0, position: 'absolute'}}
                    />
                    <div style={{
                      width: '20px',
                      height: '20px',
                      border: '2px solid #e5e7eb',
                      borderRadius: '4px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: agreedToTerms ? '#3b82f6' : 'white',
                      borderColor: agreedToTerms ? '#3b82f6' : '#e5e7eb'
                    }}>
                      {agreedToTerms && (
                        <CheckIcon style={{height: '12px', width: '12px', color: 'white'}} />
                      )}
                    </div>
                  </div>
                  <span style={{color: '#374151', fontSize: '14px', lineHeight: '1.5'}}>
                    I agree to the{' '}
                    <span style={{color: '#3b82f6', textDecoration: 'underline'}}>Terms of Service</span>
                    {' '}and{' '}
                    <span style={{color: '#3b82f6', textDecoration: 'underline'}}>Privacy Policy</span>
                  </span>
                </label>
                {errors.terms && (
                  <p style={{color: '#ef4444', fontSize: '14px', marginTop: '4px'}}>{errors.terms}</p>
                )}
              </div>

              {/* Submit Button */}
              <button
                type="submit"
                style={buttonStyle}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-1px)';
                  e.currentTarget.style.boxShadow = '0 6px 12px rgba(0, 0, 0, 0.15)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1)';
                }}
              >
                Create Account
              </button>

              {/* Sign In Link */}
              <p style={{textAlign: 'center', color: '#6b7280', marginTop: '20px', fontSize: '14px'}}>
                Already have an account?{' '}
                <button
                  type="button"
                  onClick={onSwitchToLogin}
                  style={{
                    color: '#3b82f6',
                    textDecoration: 'underline',
                    border: 'none',
                    background: 'transparent',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                >
                  Sign in here
                </button>
              </p>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignUpPage;
