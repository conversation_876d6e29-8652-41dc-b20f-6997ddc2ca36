import { YoutubeTranscript } from 'youtube-transcript';

export interface VideoInfo {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  duration: string;
  channelName: string;
  channelId: string;
  viewCount: string;
  publishedAt: string;
  embedUrl: string;
}

export interface CaptionSegment {
  text: string;
  start: number;
  duration: number;
  offset: number;
}

export class YouTubeService {
  private static readonly CORS_PROXY = 'https://cors-anywhere.herokuapp.com/';
  private static readonly YOUTUBE_API_BASE = 'https://www.googleapis.com/youtube/v3';
  
  // Extract video ID from various YouTube URL formats
  static extractVideoId(url: string): string | null {
    const patterns = [
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/|youtube\.com\/v\/)([^&\n?#]+)/,
      /youtube\.com\/watch\?.*v=([^&\n?#]+)/
    ];
    
    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match && match[1]) {
        return match[1];
      }
    }
    return null;
  }

  // Get video information using oEmbed API (no API key required)
  static async getVideoInfo(videoId: string): Promise<VideoInfo> {
    try {
      // Use YouTube oEmbed API for basic info
      const oembedUrl = `https://www.youtube.com/oembed?url=https://www.youtube.com/watch?v=${videoId}&format=json`;
      
      const response = await fetch(oembedUrl);
      if (!response.ok) {
        throw new Error('Video not found or unavailable');
      }
      
      const data = await response.json();
      
      // Get additional info by scraping the YouTube page
      const pageInfo = await this.scrapeVideoPage(videoId);
      
      return {
        id: videoId,
        title: data.title || 'Unknown Title',
        description: pageInfo.description || '',
        thumbnail: data.thumbnail_url || `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,
        duration: pageInfo.duration || 'Unknown',
        channelName: data.author_name || 'Unknown Channel',
        channelId: pageInfo.channelId || '',
        viewCount: pageInfo.viewCount || '0',
        publishedAt: pageInfo.publishedAt || '',
        embedUrl: `https://www.youtube.com/embed/${videoId}`
      };
    } catch (error) {
      console.error('Error fetching video info:', error);
      throw new Error('Failed to fetch video information. Please check the URL and try again.');
    }
  }

  // Scrape additional video information from YouTube page
  private static async scrapeVideoPage(videoId: string): Promise<any> {
    try {
      const url = `https://www.youtube.com/watch?v=${videoId}`;
      const response = await fetch(url);
      const html = await response.text();
      
      // Extract JSON data from the page
      const jsonMatch = html.match(/var ytInitialPlayerResponse = ({.+?});/);
      if (jsonMatch) {
        const playerData = JSON.parse(jsonMatch[1]);
        const videoDetails = playerData.videoDetails;
        
        if (videoDetails) {
          return {
            description: videoDetails.shortDescription || '',
            duration: this.formatDuration(parseInt(videoDetails.lengthSeconds) || 0),
            channelId: videoDetails.channelId || '',
            viewCount: this.formatViewCount(videoDetails.viewCount || '0'),
            publishedAt: new Date().toISOString() // Fallback
          };
        }
      }
      
      return {};
    } catch (error) {
      console.error('Error scraping video page:', error);
      return {};
    }
  }

  // Extract captions/transcript from YouTube video
  static async getCaptions(videoId: string): Promise<CaptionSegment[]> {
    try {
      console.log('Fetching transcript for video:', videoId);
      
      // Try to get transcript using youtube-transcript
      const transcript = await YoutubeTranscript.fetchTranscript(videoId, {
        lang: 'en', // Default to English
        country: 'US'
      });
      
      if (!transcript || transcript.length === 0) {
        throw new Error('No captions available for this video');
      }
      
      // Convert to our format
      const captions: CaptionSegment[] = transcript.map((item: any, index: number) => ({
        text: item.text || '',
        start: item.offset ? item.offset / 1000 : 0, // Convert ms to seconds
        duration: item.duration ? item.duration / 1000 : 3, // Convert ms to seconds
        offset: item.offset || 0
      }));
      
      console.log(`Successfully extracted ${captions.length} caption segments`);
      return captions;
      
    } catch (error) {
      console.error('Error fetching captions:', error);
      
      // Try alternative method or provide fallback
      try {
        return await this.getCaptionsAlternative(videoId);
      } catch (altError) {
        console.error('Alternative caption method failed:', altError);
        throw new Error('No captions available for this video. The video may not have closed captions enabled.');
      }
    }
  }

  // Alternative method to get captions
  private static async getCaptionsAlternative(videoId: string): Promise<CaptionSegment[]> {
    try {
      // Try different language codes
      const languages = ['en', 'en-US', 'en-GB'];
      
      for (const lang of languages) {
        try {
          const transcript = await YoutubeTranscript.fetchTranscript(videoId, {
            lang: lang
          });
          
          if (transcript && transcript.length > 0) {
            return transcript.map((item: any) => ({
              text: item.text || '',
              start: item.offset ? item.offset / 1000 : 0,
              duration: item.duration ? item.duration / 1000 : 3,
              offset: item.offset || 0
            }));
          }
        } catch (langError) {
          console.log(`Failed to get transcript in ${lang}:`, langError);
          continue;
        }
      }
      
      throw new Error('No captions found in any supported language');
    } catch (error) {
      throw new Error('Unable to extract captions from this video');
    }
  }

  // Get full transcript as text
  static async getFullTranscript(videoId: string): Promise<string> {
    try {
      const captions = await this.getCaptions(videoId);
      return captions.map(caption => caption.text).join(' ');
    } catch (error) {
      throw error;
    }
  }

  // Utility function to format duration
  private static formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
  }

  // Utility function to format view count
  private static formatViewCount(viewCount: string): string {
    const count = parseInt(viewCount);
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M views`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K views`;
    } else {
      return `${count} views`;
    }
  }

  // Format time for display
  static formatTime(seconds: number): string {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }

  // Validate YouTube URL
  static isValidYouTubeUrl(url: string): boolean {
    const videoId = this.extractVideoId(url);
    return videoId !== null && videoId.length === 11;
  }

  // Get video thumbnail URL
  static getThumbnailUrl(videoId: string, quality: 'default' | 'medium' | 'high' | 'standard' | 'maxres' = 'maxres'): string {
    return `https://img.youtube.com/vi/${videoId}/${quality}default.jpg`;
  }
}
