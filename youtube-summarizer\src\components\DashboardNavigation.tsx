import React from 'react';
import { 
  ChartBarIcon, 
  DocumentTextIcon, 
  CloudIcon, 
  KeyIcon, 
  CogIcon,
  ArrowRightOnRectangleIcon,
  UserCircleIcon
} from '@heroicons/react/24/outline';

interface DashboardNavigationProps {
  currentSection: string;
  onSectionChange: (section: string) => void;
  onLogout: () => void;
}

const DashboardNavigation: React.FC<DashboardNavigationProps> = ({ 
  currentSection, 
  onSectionChange, 
  onLogout 
}) => {
  const sidebarItems = [
    { id: 'overview', icon: ChartBarIcon, label: 'Overview', description: 'Dashboard overview' },
    { id: 'summaries', icon: DocumentTextIcon, label: 'My Summaries', description: 'View all summaries' },
    { id: 'storage', icon: CloudIcon, label: 'Storage', description: 'Manage your files' },
    { id: 'api', icon: KeyIcon, label: 'API Keys', description: 'API configuration' },
    { id: 'settings', icon: CogIcon, label: 'Settings', description: 'Account settings' }
  ];

  return (
    <div className="fixed left-0 top-0 h-full w-64 bg-white border-r border-gray-200 shadow-sm z-40">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-xl">📹</span>
          </div>
          <div>
            <h1 className="text-lg font-bold text-gray-900">VideoSummarizer</h1>
            <p className="text-xs text-gray-500">Dashboard</p>
          </div>
        </div>
      </div>

      {/* User Profile Section */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <UserCircleIcon className="h-10 w-10 text-gray-400" />
          <div>
            <p className="text-sm font-medium text-gray-900">John Doe</p>
            <p className="text-xs text-gray-500"><EMAIL></p>
          </div>
        </div>
      </div>

      {/* Navigation Items */}
      <nav className="flex-1 p-4">
        <div className="space-y-2">
          {sidebarItems.map((item) => {
            const IconComponent = item.icon;
            const isActive = currentSection === item.id;
            
            return (
              <button
                key={item.id}
                onClick={() => onSectionChange(item.id)}
                className={`
                  w-full flex items-center px-4 py-3 text-left rounded-lg transition-all duration-200 group
                  ${isActive 
                    ? 'bg-blue-50 text-blue-700 border-l-4 border-blue-500' 
                    : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'
                  }
                `}
              >
                <IconComponent 
                  className={`h-5 w-5 mr-3 ${isActive ? 'text-blue-600' : 'text-gray-400 group-hover:text-blue-500'}`} 
                />
                <div className="flex-1">
                  <div className={`text-sm font-medium ${isActive ? 'text-blue-700' : 'text-gray-900'}`}>
                    {item.label}
                  </div>
                  <div className="text-xs text-gray-500 mt-0.5">
                    {item.description}
                  </div>
                </div>
              </button>
            );
          })}
        </div>
      </nav>

      {/* Footer with Logout */}
      <div className="p-4 border-t border-gray-200">
        <button
          onClick={onLogout}
          className="w-full flex items-center px-4 py-3 text-left rounded-lg text-red-600 hover:bg-red-50 transition-all duration-200 group"
        >
          <ArrowRightOnRectangleIcon className="h-5 w-5 mr-3 text-red-500" />
          <span className="text-sm font-medium">Logout</span>
        </button>
        
        {/* Version info */}
        <div className="mt-4 px-4">
          <p className="text-xs text-gray-400">Version 1.0.0</p>
        </div>
      </div>
    </div>
  );
};

export default DashboardNavigation;
