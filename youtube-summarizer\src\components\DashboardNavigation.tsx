import React from 'react';
import { 
  ChartBarIcon, 
  DocumentTextIcon, 
  CloudIcon, 
  KeyIcon, 
  CogIcon,
  ArrowRightOnRectangleIcon,
  UserCircleIcon
} from '@heroicons/react/24/outline';

interface DashboardNavigationProps {
  currentSection: string;
  onSectionChange: (section: string) => void;
  onLogout: () => void;
}

const DashboardNavigation: React.FC<DashboardNavigationProps> = ({ 
  currentSection, 
  onSectionChange, 
  onLogout 
}) => {
  const sidebarItems = [
    { id: 'overview', icon: ChartBarIcon, label: 'Overview', description: 'Dashboard overview' },
    { id: 'summaries', icon: DocumentTextIcon, label: 'My Summaries', description: 'View all summaries' },
    { id: 'storage', icon: CloudIcon, label: 'Storage', description: 'Manage your files' },
    { id: 'api', icon: KeyIcon, label: 'API Keys', description: 'API configuration' },
    { id: 'settings', icon: CogIcon, label: 'Settings', description: 'Account settings' }
  ];

  const sidebarStyle = {
    position: 'fixed' as const,
    left: 0,
    top: 0,
    height: '100vh',
    width: '256px',
    backgroundColor: 'white',
    borderRight: '1px solid #e5e7eb',
    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
    zIndex: 40
  };

  const headerStyle = {
    padding: '24px',
    borderBottom: '1px solid #e5e7eb'
  };

  const logoContainerStyle = {
    display: 'flex',
    alignItems: 'center',
    gap: '12px'
  };

  const logoIconStyle = {
    width: '40px',
    height: '40px',
    background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
    borderRadius: '8px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  };

  return (
    <div style={sidebarStyle}>
      {/* Header */}
      <div style={headerStyle}>
        <div style={logoContainerStyle}>
          <div style={logoIconStyle}>
            <span style={{color: 'white', fontWeight: 'bold', fontSize: '20px'}}>📹</span>
          </div>
          <div>
            <h1 style={{fontSize: '18px', fontWeight: 'bold', color: '#111827'}}>VidSum</h1>
            <p style={{fontSize: '12px', color: '#6b7280'}}>Dashboard</p>
          </div>
        </div>
      </div>

      {/* User Profile Section */}
      <div style={{padding: '16px', borderBottom: '1px solid #e5e7eb'}}>
        <div style={{display: 'flex', alignItems: 'center', gap: '12px'}}>
          <UserCircleIcon style={{height: '40px', width: '40px', color: '#9ca3af'}} />
          <div>
            <p style={{fontSize: '14px', fontWeight: '500', color: '#111827'}}>John Doe</p>
            <p style={{fontSize: '12px', color: '#6b7280'}}><EMAIL></p>
          </div>
        </div>
      </div>

      {/* Navigation Items */}
      <nav style={{flex: 1, padding: '16px'}}>
        <div style={{display: 'flex', flexDirection: 'column', gap: '8px'}}>
          {sidebarItems.map((item) => {
            const IconComponent = item.icon;
            const isActive = currentSection === item.id;

            return (
              <button
                key={item.id}
                onClick={() => onSectionChange(item.id)}
                style={{
                  width: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  padding: '12px 16px',
                  textAlign: 'left',
                  borderRadius: '8px',
                  transition: 'all 0.2s ease',
                  border: 'none',
                  cursor: 'pointer',
                  backgroundColor: isActive ? '#eff6ff' : 'transparent',
                  color: isActive ? '#1d4ed8' : '#374151',
                  borderLeft: isActive ? '4px solid #3b82f6' : '4px solid transparent'
                }}
                onMouseEnter={(e) => {
                  if (!isActive) {
                    e.currentTarget.style.backgroundColor = '#f9fafb';
                    e.currentTarget.style.color = '#2563eb';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isActive) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = '#374151';
                  }
                }}
              >
                <IconComponent
                  style={{
                    height: '20px',
                    width: '20px',
                    marginRight: '12px',
                    color: isActive ? '#2563eb' : '#9ca3af'
                  }}
                />
                <div style={{flex: 1}}>
                  <div style={{
                    fontSize: '14px',
                    fontWeight: '500',
                    color: isActive ? '#1d4ed8' : '#111827'
                  }}>
                    {item.label}
                  </div>
                  <div style={{fontSize: '12px', color: '#6b7280', marginTop: '2px'}}>
                    {item.description}
                  </div>
                </div>
              </button>
            );
          })}
        </div>
      </nav>

      {/* Footer with Logout */}
      <div style={{padding: '16px', borderTop: '1px solid #e5e7eb'}}>
        <button
          onClick={onLogout}
          style={{
            width: '100%',
            display: 'flex',
            alignItems: 'center',
            padding: '12px 16px',
            textAlign: 'left',
            borderRadius: '8px',
            color: '#dc2626',
            border: 'none',
            background: 'transparent',
            cursor: 'pointer',
            transition: 'all 0.2s ease'
          }}
          onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#fef2f2'}
          onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
        >
          <ArrowRightOnRectangleIcon style={{height: '20px', width: '20px', marginRight: '12px', color: '#ef4444'}} />
          <span style={{fontSize: '14px', fontWeight: '500'}}>Logout</span>
        </button>

        {/* Version info */}
        <div style={{marginTop: '16px', padding: '0 16px'}}>
          <p style={{fontSize: '12px', color: '#9ca3af'}}>Version 1.0.0</p>
        </div>
      </div>
    </div>
  );
};

export default DashboardNavigation;
