{"hash": "99dbde2b", "configHash": "6d487fc3", "lockfileHash": "78159426", "browserHash": "590e1bd2", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "36bc027a", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "b2db6a67", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "0e9946fb", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "299b2c8a", "needsInterop": true}, "@heroicons/react/24/outline": {"src": "../../@heroicons/react/24/outline/esm/index.js", "file": "@heroicons_react_24_outline.js", "fileHash": "ce37d299", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "1dd10c86", "needsInterop": true}, "youtube-transcript": {"src": "../../youtube-transcript/dist/youtube-transcript.esm.js", "file": "youtube-transcript.js", "fileHash": "c7465bc6", "needsInterop": false}}, "chunks": {"chunk-E7Q22A2S": {"file": "chunk-E7Q22A2S.js"}, "chunk-6P6Q65E3": {"file": "chunk-6P6Q65E3.js"}, "chunk-5WRI5ZAA": {"file": "chunk-5WRI5ZAA.js"}}}