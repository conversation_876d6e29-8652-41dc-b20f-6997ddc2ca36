import React, { useState } from 'react';

function App() {
  const [currentPage, setCurrentPage] = useState('home');
  const [dashboardSection, setDashboardSection] = useState('overview');

  const buttonStyle = {
    backgroundColor: '#3b82f6',
    color: 'white',
    padding: '12px 24px',
    border: 'none',
    borderRadius: '8px',
    fontSize: '16px',
    cursor: 'pointer',
    margin: '5px',
    transition: 'all 0.3s ease'
  };

  const navStyle = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '15px 30px',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    backdropFilter: 'blur(10px)',
    borderBottom: '1px solid rgba(229, 231, 235, 0.3)',
    position: 'fixed' as const,
    top: '0',
    left: '0',
    right: '0',
    zIndex: 1000,
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05)'
  };

  const navLinkStyle = {
    color: '#374151',
    textDecoration: 'none',
    padding: '8px 16px',
    borderRadius: '6px',
    transition: 'all 0.3s ease',
    cursor: 'pointer',
    fontWeight: '500'
  };

  const containerStyle = {
    maxWidth: '1200px',
    margin: '0 auto',
    padding: '0 20px'
  };

  if (currentPage === 'login') {
    return (
      <div>
        <nav style={navStyle}>
          <div style={{fontSize: '24px', fontWeight: 'bold', color: '#1f2937'}}>
            📹 VideoSummarizer
          </div>
          <button
            style={{...buttonStyle, backgroundColor: '#6b7280'}}
            onClick={() => setCurrentPage('home')}
          >
            Back to Home
          </button>
        </nav>
        <div style={{...containerStyle, padding: '40px 20px'}}>
          <div style={{maxWidth: '400px', margin: '0 auto', backgroundColor: 'white', padding: '40px', borderRadius: '12px', boxShadow: '0 4px 6px rgba(0,0,0,0.1)'}}>
            <h2 style={{fontSize: '28px', marginBottom: '20px', textAlign: 'center', color: '#1f2937'}}>Welcome Back</h2>
            <p style={{textAlign: 'center', color: '#6b7280', marginBottom: '30px'}}>Sign in to your account</p>

            <div style={{marginBottom: '20px'}}>
              <label style={{display: 'block', marginBottom: '8px', fontWeight: '500', color: '#374151'}}>Email</label>
              <input
                type="email"
                placeholder="Enter your email"
                style={{
                  width: '100%',
                  padding: '12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '16px',
                  boxSizing: 'border-box'
                }}
              />
            </div>

            <div style={{marginBottom: '30px'}}>
              <label style={{display: 'block', marginBottom: '8px', fontWeight: '500', color: '#374151'}}>Password</label>
              <input
                type="password"
                placeholder="Enter your password"
                style={{
                  width: '100%',
                  padding: '12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '16px',
                  boxSizing: 'border-box'
                }}
              />
            </div>

            <button
              style={{...buttonStyle, width: '100%', marginBottom: '20px'}}
              onClick={() => setCurrentPage('dashboard')}
            >
              Sign In
            </button>

            <p style={{textAlign: 'center', color: '#6b7280'}}>
              Don't have an account? <span style={{color: '#3b82f6', cursor: 'pointer'}}>Sign up</span>
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (currentPage === 'dashboard') {
    const sidebarItems = [
      { id: 'overview', icon: '📊', label: 'Overview' },
      { id: 'summaries', icon: '📝', label: 'My Summaries' },
      { id: 'storage', icon: '💾', label: 'Storage' },
      { id: 'api', icon: '🔌', label: 'API Keys' },
      { id: 'settings', icon: '⚙️', label: 'Settings' }
    ];

    const renderDashboardContent = () => {
      switch (dashboardSection) {
        case 'overview':
          return (
            <div>
              <h1 style={{fontSize: '32px', marginBottom: '10px', color: '#1f2937'}}>Dashboard Overview</h1>
              <p style={{color: '#6b7280', marginBottom: '40px'}}>Transform YouTube videos into instant summaries</p>

              {/* Stats */}
              <div style={{display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '20px', marginBottom: '40px'}}>
                <div style={{backgroundColor: 'white', padding: '24px', borderRadius: '12px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)'}}>
                  <h3 style={{color: '#1f2937', marginBottom: '8px'}}>Total Summaries</h3>
                  <p style={{fontSize: '32px', fontWeight: 'bold', color: '#3b82f6', margin: '0'}}>24</p>
                </div>
                <div style={{backgroundColor: 'white', padding: '24px', borderRadius: '12px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)'}}>
                  <h3 style={{color: '#1f2937', marginBottom: '8px'}}>Time Saved</h3>
                  <p style={{fontSize: '32px', fontWeight: 'bold', color: '#10b981', margin: '0'}}>8.5h</p>
                </div>
                <div style={{backgroundColor: 'white', padding: '24px', borderRadius: '12px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)'}}>
                  <h3 style={{color: '#1f2937', marginBottom: '8px'}}>Downloads</h3>
                  <p style={{fontSize: '32px', fontWeight: 'bold', color: '#8b5cf6', margin: '0'}}>18</p>
                </div>
              </div>

              {/* New Summary Form */}
              <div style={{backgroundColor: 'white', padding: '30px', borderRadius: '12px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)', marginBottom: '30px'}}>
                <h2 style={{fontSize: '24px', marginBottom: '20px', color: '#1f2937'}}>Create New Summary</h2>
                <div style={{display: 'flex', gap: '15px', flexWrap: 'wrap'}}>
                  <input
                    type="url"
                    placeholder="Paste YouTube video URL here..."
                    style={{
                      flex: '1',
                      minWidth: '300px',
                      padding: '15px',
                      border: '1px solid #d1d5db',
                      borderRadius: '8px',
                      fontSize: '16px'
                    }}
                  />
                  <button style={{...buttonStyle, padding: '15px 30px'}}>
                    Generate Summary
                  </button>
                </div>
              </div>
            </div>
          );

        case 'summaries':
          return (
            <div>
              <h1 style={{fontSize: '32px', marginBottom: '10px', color: '#1f2937'}}>My Summaries</h1>
              <p style={{color: '#6b7280', marginBottom: '40px'}}>Manage all your video summaries</p>

              <div style={{backgroundColor: 'white', padding: '30px', borderRadius: '12px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)'}}>
                <div style={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px'}}>
                  <h2 style={{fontSize: '24px', color: '#1f2937', margin: '0'}}>Recent Summaries</h2>
                  <button style={{...buttonStyle, padding: '8px 16px', fontSize: '14px'}}>Export All</button>
                </div>
                {[
                  {title: 'How to Build a React App with TypeScript', status: 'completed', time: '2 hours ago', duration: '15:30'},
                  {title: 'Advanced JavaScript Concepts Explained', status: 'processing', time: '1 hour ago', duration: '22:45'},
                  {title: 'CSS Grid vs Flexbox: Complete Guide', status: 'completed', time: '3 hours ago', duration: '18:20'},
                  {title: 'Python for Beginners - Complete Course', status: 'completed', time: '1 day ago', duration: '45:12'},
                  {title: 'Machine Learning Fundamentals', status: 'completed', time: '2 days ago', duration: '32:18'}
                ].map((item, index) => (
                  <div key={index} style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '20px',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    marginBottom: '15px',
                    backgroundColor: '#fafafa'
                  }}>
                    <div style={{flex: 1}}>
                      <h3 style={{margin: '0 0 8px 0', color: '#1f2937', fontSize: '18px'}}>{item.title}</h3>
                      <p style={{margin: '0', color: '#6b7280', fontSize: '14px'}}>{item.time} • Duration: {item.duration}</p>
                    </div>
                    <div style={{display: 'flex', alignItems: 'center', gap: '15px'}}>
                      {item.status === 'completed' ? (
                        <>
                          <span style={{color: '#10b981', fontWeight: '500', fontSize: '14px'}}>✅ Completed</span>
                          <button style={{...buttonStyle, padding: '6px 12px', fontSize: '12px', backgroundColor: '#10b981'}}>View</button>
                          <button style={{...buttonStyle, padding: '6px 12px', fontSize: '12px', backgroundColor: '#6b7280'}}>Download</button>
                        </>
                      ) : (
                        <span style={{color: '#f59e0b', fontWeight: '500', fontSize: '14px'}}>⏳ Processing</span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          );

        case 'storage':
          return (
            <div>
              <h1 style={{fontSize: '32px', marginBottom: '10px', color: '#1f2937'}}>Storage Management</h1>
              <p style={{color: '#6b7280', marginBottom: '40px'}}>Monitor your storage usage and manage files</p>

              <div style={{display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px', marginBottom: '30px'}}>
                <div style={{backgroundColor: 'white', padding: '24px', borderRadius: '12px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)'}}>
                  <h3 style={{color: '#1f2937', marginBottom: '15px'}}>Storage Usage</h3>
                  <div style={{backgroundColor: '#f3f4f6', borderRadius: '10px', height: '10px', marginBottom: '10px'}}>
                    <div style={{backgroundColor: '#3b82f6', borderRadius: '10px', height: '10px', width: '65%'}}></div>
                  </div>
                  <p style={{color: '#6b7280', fontSize: '14px', margin: '0'}}>6.5 GB of 10 GB used</p>
                </div>
                <div style={{backgroundColor: 'white', padding: '24px', borderRadius: '12px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)'}}>
                  <h3 style={{color: '#1f2937', marginBottom: '8px'}}>Total Files</h3>
                  <p style={{fontSize: '32px', fontWeight: 'bold', color: '#8b5cf6', margin: '0'}}>156</p>
                </div>
              </div>

              <div style={{backgroundColor: 'white', padding: '30px', borderRadius: '12px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)'}}>
                <h2 style={{fontSize: '24px', marginBottom: '20px', color: '#1f2937'}}>File Management</h2>
                <div style={{display: 'flex', gap: '10px', marginBottom: '20px'}}>
                  <button style={{...buttonStyle, backgroundColor: '#10b981'}}>Clean Up Old Files</button>
                  <button style={{...buttonStyle, backgroundColor: '#f59e0b'}}>Export All Data</button>
                </div>
                <p style={{color: '#6b7280'}}>Automatically clean up files older than 30 days to free up space.</p>
              </div>
            </div>
          );

        case 'api':
          return (
            <div>
              <h1 style={{fontSize: '32px', marginBottom: '10px', color: '#1f2937'}}>API Management</h1>
              <p style={{color: '#6b7280', marginBottom: '40px'}}>Manage your API keys and integration settings</p>

              <div style={{backgroundColor: 'white', padding: '30px', borderRadius: '12px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)', marginBottom: '30px'}}>
                <h2 style={{fontSize: '24px', marginBottom: '20px', color: '#1f2937'}}>API Keys</h2>
                <div style={{marginBottom: '20px'}}>
                  <label style={{display: 'block', marginBottom: '8px', fontWeight: '500', color: '#374151'}}>Production API Key</label>
                  <div style={{display: 'flex', gap: '10px'}}>
                    <input
                      type="text"
                      value="sk-proj-xxxxxxxxxxxxxxxxxxxxxxxx"
                      readOnly
                      style={{
                        flex: '1',
                        padding: '12px',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        fontSize: '14px',
                        backgroundColor: '#f9fafb'
                      }}
                    />
                    <button style={{...buttonStyle, padding: '12px 20px'}}>Copy</button>
                    <button style={{...buttonStyle, backgroundColor: '#ef4444', padding: '12px 20px'}}>Regenerate</button>
                  </div>
                </div>
                <div style={{backgroundColor: '#fef3c7', padding: '15px', borderRadius: '8px', border: '1px solid #fbbf24'}}>
                  <p style={{color: '#92400e', margin: '0', fontSize: '14px'}}>
                    ⚠️ Keep your API keys secure. Never share them publicly or commit them to version control.
                  </p>
                </div>
              </div>

              <div style={{backgroundColor: 'white', padding: '30px', borderRadius: '12px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)'}}>
                <h2 style={{fontSize: '24px', marginBottom: '20px', color: '#1f2937'}}>API Usage</h2>
                <div style={{display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '20px'}}>
                  <div>
                    <h3 style={{color: '#1f2937', marginBottom: '8px'}}>Requests This Month</h3>
                    <p style={{fontSize: '24px', fontWeight: 'bold', color: '#3b82f6', margin: '0'}}>1,247</p>
                  </div>
                  <div>
                    <h3 style={{color: '#1f2937', marginBottom: '8px'}}>Rate Limit</h3>
                    <p style={{fontSize: '24px', fontWeight: 'bold', color: '#10b981', margin: '0'}}>100/min</p>
                  </div>
                </div>
              </div>
            </div>
          );

        case 'settings':
          return (
            <div>
              <h1 style={{fontSize: '32px', marginBottom: '10px', color: '#1f2937'}}>Settings</h1>
              <p style={{color: '#6b7280', marginBottom: '40px'}}>Manage your account and preferences</p>

              <div style={{display: 'grid', gap: '30px'}}>
                <div style={{backgroundColor: 'white', padding: '30px', borderRadius: '12px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)'}}>
                  <h2 style={{fontSize: '24px', marginBottom: '20px', color: '#1f2937'}}>Profile Settings</h2>
                  <div style={{marginBottom: '20px'}}>
                    <label style={{display: 'block', marginBottom: '8px', fontWeight: '500', color: '#374151'}}>Full Name</label>
                    <input
                      type="text"
                      defaultValue="John Doe"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        fontSize: '16px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div style={{marginBottom: '20px'}}>
                    <label style={{display: 'block', marginBottom: '8px', fontWeight: '500', color: '#374151'}}>Email</label>
                    <input
                      type="email"
                      defaultValue="<EMAIL>"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        fontSize: '16px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <button style={buttonStyle}>Save Changes</button>
                </div>

                <div style={{backgroundColor: 'white', padding: '30px', borderRadius: '12px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)'}}>
                  <h2 style={{fontSize: '24px', marginBottom: '20px', color: '#1f2937'}}>Preferences</h2>
                  <div style={{marginBottom: '15px'}}>
                    <label style={{display: 'flex', alignItems: 'center', cursor: 'pointer'}}>
                      <input type="checkbox" defaultChecked style={{marginRight: '10px'}} />
                      <span style={{color: '#374151'}}>Email notifications for completed summaries</span>
                    </label>
                  </div>
                  <div style={{marginBottom: '15px'}}>
                    <label style={{display: 'flex', alignItems: 'center', cursor: 'pointer'}}>
                      <input type="checkbox" defaultChecked style={{marginRight: '10px'}} />
                      <span style={{color: '#374151'}}>Auto-delete summaries after 90 days</span>
                    </label>
                  </div>
                  <div style={{marginBottom: '20px'}}>
                    <label style={{display: 'flex', alignItems: 'center', cursor: 'pointer'}}>
                      <input type="checkbox" style={{marginRight: '10px'}} />
                      <span style={{color: '#374151'}}>Marketing emails and updates</span>
                    </label>
                  </div>
                  <button style={buttonStyle}>Update Preferences</button>
                </div>
              </div>
            </div>
          );

        default:
          return <div>Page not found</div>;
      }
    };

    return (
      <div style={{display: 'flex', minHeight: '100vh', backgroundColor: '#f8fafc'}}>
        {/* Sidebar */}
        <div style={{
          width: '280px',
          backgroundColor: 'white',
          borderRight: '1px solid #e5e7eb',
          padding: '20px 0',
          position: 'fixed',
          height: '100vh',
          overflowY: 'auto'
        }}>
          <div style={{padding: '0 20px', marginBottom: '30px'}}>
            <div style={{fontSize: '24px', fontWeight: 'bold', color: '#1f2937'}}>
              📹 VideoSummarizer
            </div>
          </div>

          <nav>
            {sidebarItems.map((item) => (
              <button
                key={item.id}
                onClick={() => setDashboardSection(item.id)}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  width: '100%',
                  padding: '12px 20px',
                  border: 'none',
                  backgroundColor: dashboardSection === item.id ? '#eff6ff' : 'transparent',
                  color: dashboardSection === item.id ? '#3b82f6' : '#374151',
                  cursor: 'pointer',
                  fontSize: '16px',
                  textAlign: 'left',
                  transition: 'all 0.3s ease',
                  borderLeft: dashboardSection === item.id ? '3px solid #3b82f6' : '3px solid transparent'
                }}
                onMouseEnter={(e) => {
                  if (dashboardSection !== item.id) {
                    e.currentTarget.style.backgroundColor = '#f9fafb';
                  }
                }}
                onMouseLeave={(e) => {
                  if (dashboardSection !== item.id) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }
                }}
              >
                <span style={{marginRight: '12px', fontSize: '18px'}}>{item.icon}</span>
                {item.label}
              </button>
            ))}
          </nav>

          <div style={{position: 'absolute', bottom: '20px', left: '20px', right: '20px'}}>
            <button
              style={{
                ...buttonStyle,
                backgroundColor: '#ef4444',
                width: '100%',
                margin: '0'
              }}
              onClick={() => setCurrentPage('home')}
            >
              🚪 Logout
            </button>
          </div>
        </div>

        {/* Main Content */}
        <div style={{marginLeft: '280px', flex: 1, padding: '40px'}}>
          {renderDashboardContent()}
        </div>
      </div>
    );
  }

  // Home page
  return (
    <div>
      {/* Enhanced Navigation */}
      <nav style={navStyle}>
        <div style={{display: 'flex', alignItems: 'center'}}>
          <div style={{fontSize: '28px', fontWeight: 'bold', color: '#1f2937', marginRight: '40px'}}>
            📹 VideoSummarizer
          </div>
          <div style={{display: 'flex', alignItems: 'center', gap: '30px'}}>
            <a href="#features" style={{...navLinkStyle}}
               onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f3f4f6'}
               onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}>
              Features
            </a>
            <a href="#pricing" style={{...navLinkStyle}}
               onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f3f4f6'}
               onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}>
              Pricing
            </a>
            <a href="#testimonials" style={{...navLinkStyle}}
               onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f3f4f6'}
               onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}>
              Testimonials
            </a>
            <a href="#about" style={{...navLinkStyle}}
               onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f3f4f6'}
               onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}>
              About
            </a>
          </div>
        </div>
        <div style={{display: 'flex', alignItems: 'center', gap: '15px'}}>
          <button
            style={{
              ...navLinkStyle,
              border: '1px solid #d1d5db',
              borderRadius: '8px',
              padding: '8px 20px',
              backgroundColor: 'transparent'
            }}
            onClick={() => setCurrentPage('login')}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#f3f4f6';
              e.currentTarget.style.borderColor = '#9ca3af';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
              e.currentTarget.style.borderColor = '#d1d5db';
            }}
          >
            Sign In
          </button>
          <button
            style={{
              ...buttonStyle,
              margin: '0',
              padding: '8px 20px',
              fontSize: '14px',
              fontWeight: '600'
            }}
            onClick={() => setCurrentPage('login')}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#2563eb'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#3b82f6'}
          >
            Get Started Free
          </button>


        </div>
      </nav>

      {/* Add padding to account for fixed nav */}
      <div style={{paddingTop: '80px'}}>

      {/* Hero Section */}
      <div style={{background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white', padding: '80px 20px', textAlign: 'center'}}>
        <div style={containerStyle}>
          <h1 style={{fontSize: '48px', marginBottom: '20px', fontWeight: 'bold'}}>
            Transform YouTube Videos into
            <br />
            <span style={{color: '#fbbf24'}}>Instant Summaries</span>
          </h1>
          <p style={{fontSize: '20px', marginBottom: '40px', opacity: '0.9'}}>
            Extract key insights, download captions, and get AI-powered summaries from any YouTube video in seconds.
          </p>
          <button
            style={{...buttonStyle, fontSize: '18px', padding: '15px 30px', backgroundColor: '#fbbf24', color: '#1f2937'}}
            onClick={() => setCurrentPage('login')}
          >
            Get Started Free
          </button>
        </div>
      </div>

      {/* Features Section */}
      <div id="features" style={{padding: '80px 20px', backgroundColor: 'white'}}>
        <div style={containerStyle}>
          <h2 style={{fontSize: '36px', textAlign: 'center', marginBottom: '50px', color: '#1f2937'}}>
            Powerful Features for Content Creators
          </h2>
          <div style={{display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '40px'}}>
            <div style={{textAlign: 'center', padding: '30px'}}>
              <div style={{fontSize: '48px', marginBottom: '20px'}}>✨</div>
              <h3 style={{fontSize: '24px', marginBottom: '15px', color: '#1f2937'}}>AI-Powered Summaries</h3>
              <p style={{color: '#6b7280', lineHeight: '1.6'}}>
                Get intelligent, contextual summaries that capture the key points and insights from any video content.
              </p>
            </div>
            <div style={{textAlign: 'center', padding: '30px'}}>
              <div style={{fontSize: '48px', marginBottom: '20px'}}>📥</div>
              <h3 style={{fontSize: '24px', marginBottom: '15px', color: '#1f2937'}}>Caption Download</h3>
              <p style={{color: '#6b7280', lineHeight: '1.6'}}>
                Download closed captions and subtitles in multiple formats for offline access and further processing.
              </p>
            </div>
            <div style={{textAlign: 'center', padding: '30px'}}>
              <div style={{fontSize: '48px', marginBottom: '20px'}}>📝</div>
              <h3 style={{fontSize: '24px', marginBottom: '15px', color: '#1f2937'}}>Smart Transcription</h3>
              <p style={{color: '#6b7280', lineHeight: '1.6'}}>
                Advanced transcription technology that understands context and provides accurate text conversion.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Testimonials Section */}
      <div id="testimonials" style={{padding: '80px 20px', backgroundColor: '#f8fafc'}}>
        <div style={containerStyle}>
          <h2 style={{fontSize: '36px', textAlign: 'center', marginBottom: '20px', color: '#1f2937'}}>
            Loved by Students & YouTubers
          </h2>
          <p style={{fontSize: '18px', textAlign: 'center', marginBottom: '60px', color: '#6b7280'}}>
            See how VideoSummarizer is helping creators and learners save time and boost productivity
          </p>

          <div style={{display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))', gap: '30px', maxWidth: '1200px', margin: '0 auto'}}>
            {[
              {
                name: 'Sarah Chen',
                role: 'Computer Science Student',
                avatar: '👩‍💻',
                content: 'VideoSummarizer has been a game-changer for my studies! I can quickly get the key points from hour-long programming tutorials and focus on practicing instead of note-taking. Saved me 10+ hours per week!',
                rating: 5
              },
              {
                name: 'Alex Rodriguez',
                role: 'Tech YouTuber (250K subs)',
                avatar: '🎬',
                content: 'As a content creator, I use VideoSummarizer to research competitor videos and create better content. The AI summaries help me understand trends quickly and the caption downloads are perfect for creating blog posts from my videos.',
                rating: 5
              },
              {
                name: 'Emily Johnson',
                role: 'Medical Student',
                avatar: '👩‍⚕️',
                content: 'Medical lectures are often 2-3 hours long. VideoSummarizer helps me review key concepts quickly before exams. The transcription feature is incredibly accurate for medical terminology!',
                rating: 5
              },
              {
                name: 'Marcus Thompson',
                role: 'Educational YouTuber',
                avatar: '📚',
                content: 'I create educational content and VideoSummarizer helps me repurpose my long-form videos into bite-sized summaries for social media. The API integration with my workflow is seamless!',
                rating: 5
              },
              {
                name: 'Lisa Park',
                role: 'MBA Student',
                avatar: '👩‍🎓',
                content: 'Business case study videos used to take forever to review. Now I can get the essential insights in minutes and spend more time on analysis. This tool is essential for any serious student.',
                rating: 5
              },
              {
                name: 'David Kim',
                role: 'Coding Bootcamp Graduate',
                avatar: '💻',
                content: 'During my bootcamp, VideoSummarizer helped me quickly review coding tutorials and focus on the most important concepts. It was like having a study buddy that never got tired!',
                rating: 5
              }
            ].map((testimonial, index) => (
              <div key={index} style={{
                backgroundColor: 'white',
                padding: '30px',
                borderRadius: '16px',
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05)',
                border: '1px solid #e5e7eb',
                position: 'relative'
              }}>
                <div style={{display: 'flex', marginBottom: '15px'}}>
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <span key={i} style={{color: '#fbbf24', fontSize: '18px'}}>⭐</span>
                  ))}
                </div>
                <p style={{
                  color: '#374151',
                  lineHeight: '1.6',
                  marginBottom: '20px',
                  fontSize: '16px',
                  fontStyle: 'italic'
                }}>
                  "{testimonial.content}"
                </p>
                <div style={{display: 'flex', alignItems: 'center'}}>
                  <div style={{
                    fontSize: '40px',
                    marginRight: '15px',
                    width: '50px',
                    height: '50px',
                    borderRadius: '50%',
                    backgroundColor: '#f3f4f6',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    {testimonial.avatar}
                  </div>
                  <div>
                    <h4 style={{margin: '0 0 5px 0', color: '#1f2937', fontWeight: '600'}}>{testimonial.name}</h4>
                    <p style={{margin: '0', color: '#6b7280', fontSize: '14px'}}>{testimonial.role}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div style={{textAlign: 'center', marginTop: '50px'}}>
            <button
              style={{
                ...buttonStyle,
                fontSize: '18px',
                padding: '15px 30px',
                backgroundColor: '#10b981'
              }}
              onClick={() => setCurrentPage('login')}
            >
              Join 10,000+ Happy Users
            </button>
          </div>
        </div>
      </div>

      {/* Pricing Section */}
      <div id="pricing" style={{padding: '80px 20px', backgroundColor: 'white'}}>
        <div style={containerStyle}>
          <h2 style={{fontSize: '36px', textAlign: 'center', marginBottom: '50px', color: '#1f2937'}}>
            Simple, Transparent Pricing
          </h2>
          <div style={{display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '30px', maxWidth: '1000px', margin: '0 auto'}}>
            {[
              {
                name: 'Free',
                price: '$0',
                period: 'forever',
                features: ['5 video summaries per month', 'Basic caption download', 'Standard summary quality', 'Email support'],
                popular: false
              },
              {
                name: 'Pro',
                price: '$19',
                period: 'per month',
                features: ['100 video summaries per month', 'Advanced caption download', 'High-quality AI summaries', 'Priority support', 'Custom templates'],
                popular: true
              },
              {
                name: 'Enterprise',
                price: '$99',
                period: 'per month',
                features: ['Unlimited video summaries', 'Premium features', 'API access', 'Dedicated support', 'Team collaboration'],
                popular: false
              }
            ].map((plan, index) => (
              <div key={index} style={{
                backgroundColor: 'white',
                padding: '40px 30px',
                borderRadius: '12px',
                boxShadow: plan.popular ? '0 8px 25px rgba(59, 130, 246, 0.15)' : '0 4px 6px rgba(0,0,0,0.1)',
                border: plan.popular ? '2px solid #3b82f6' : '1px solid #e5e7eb',
                position: 'relative',
                textAlign: 'center'
              }}>
                {plan.popular && (
                  <div style={{
                    position: 'absolute',
                    top: '-12px',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    backgroundColor: '#3b82f6',
                    color: 'white',
                    padding: '6px 20px',
                    borderRadius: '20px',
                    fontSize: '14px',
                    fontWeight: '500'
                  }}>
                    Most Popular
                  </div>
                )}
                <h3 style={{fontSize: '24px', marginBottom: '10px', color: '#1f2937'}}>{plan.name}</h3>
                <div style={{marginBottom: '30px'}}>
                  <span style={{fontSize: '48px', fontWeight: 'bold', color: '#1f2937'}}>{plan.price}</span>
                  <span style={{color: '#6b7280', marginLeft: '8px'}}>/{plan.period}</span>
                </div>
                <ul style={{listStyle: 'none', padding: '0', marginBottom: '30px'}}>
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} style={{
                      padding: '8px 0',
                      color: '#374151',
                      borderBottom: featureIndex < plan.features.length - 1 ? '1px solid #f3f4f6' : 'none'
                    }}>
                      ✓ {feature}
                    </li>
                  ))}
                </ul>
                <button
                  style={{
                    ...buttonStyle,
                    width: '100%',
                    backgroundColor: plan.popular ? '#3b82f6' : '#6b7280'
                  }}
                  onClick={() => setCurrentPage('login')}
                >
                  {plan.name === 'Free' ? 'Get Started' : 'Start Free Trial'}
                </button>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* About Section */}
      <div id="about" style={{padding: '80px 20px', backgroundColor: '#f8fafc'}}>
        <div style={containerStyle}>
          <div style={{display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))', gap: '60px', alignItems: 'center'}}>
            <div>
              <h2 style={{fontSize: '36px', marginBottom: '20px', color: '#1f2937'}}>
                Why Choose VideoSummarizer?
              </h2>
              <p style={{fontSize: '18px', color: '#6b7280', marginBottom: '30px', lineHeight: '1.6'}}>
                In today's fast-paced world, consuming video content efficiently is crucial. VideoSummarizer leverages
                cutting-edge AI technology to help you extract maximum value from YouTube videos in minimal time.
              </p>
              <div style={{space: '20px'}}>
                {[
                  'Save hours of watching time with instant summaries',
                  'Access content offline with downloaded captions',
                  'Perfect for researchers, students, and professionals',
                  'Trusted by 10,000+ users worldwide',
                  'Enterprise-grade security and privacy'
                ].map((benefit, index) => (
                  <div key={index} style={{display: 'flex', alignItems: 'center', marginBottom: '15px'}}>
                    <div style={{
                      width: '24px',
                      height: '24px',
                      backgroundColor: '#10b981',
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginRight: '15px'
                    }}>
                      <span style={{color: 'white', fontSize: '12px', fontWeight: 'bold'}}>✓</span>
                    </div>
                    <span style={{color: '#374151', fontSize: '16px'}}>{benefit}</span>
                  </div>
                ))}
              </div>
              <button
                style={{
                  ...buttonStyle,
                  fontSize: '18px',
                  padding: '15px 30px',
                  marginTop: '20px'
                }}
                onClick={() => setCurrentPage('login')}
              >
                Start Your Free Trial
              </button>
            </div>
            <div style={{textAlign: 'center'}}>
              <div style={{
                backgroundColor: 'white',
                borderRadius: '20px',
                padding: '40px',
                boxShadow: '0 20px 25px rgba(0, 0, 0, 0.1)',
                border: '1px solid #e5e7eb'
              }}>
                <div style={{fontSize: '64px', marginBottom: '20px'}}>🚀</div>
                <h3 style={{fontSize: '24px', marginBottom: '15px', color: '#1f2937'}}>Ready to Get Started?</h3>
                <p style={{color: '#6b7280', marginBottom: '25px'}}>
                  Join thousands of users who are already transforming their video experience
                </p>
                <div style={{display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '20px'}}>
                  <div style={{textAlign: 'center'}}>
                    <div style={{fontSize: '24px', fontWeight: 'bold', color: '#3b82f6'}}>10K+</div>
                    <div style={{fontSize: '12px', color: '#6b7280'}}>Happy Users</div>
                  </div>
                  <div style={{textAlign: 'center'}}>
                    <div style={{fontSize: '24px', fontWeight: 'bold', color: '#10b981'}}>1M+</div>
                    <div style={{fontSize: '12px', color: '#6b7280'}}>Videos Processed</div>
                  </div>
                </div>
                <button
                  style={{
                    ...buttonStyle,
                    width: '100%',
                    margin: '0',
                    backgroundColor: '#10b981'
                  }}
                  onClick={() => setCurrentPage('login')}
                >
                  Get Started Now
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Footer */}
      <footer style={{backgroundColor: '#1f2937', color: 'white', padding: '60px 20px 30px'}}>
        <div style={containerStyle}>
          <div style={{display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '40px', marginBottom: '40px'}}>
            <div>
              <div style={{fontSize: '24px', fontWeight: 'bold', marginBottom: '20px'}}>
                📹 VideoSummarizer
              </div>
              <p style={{color: '#9ca3af', marginBottom: '20px', lineHeight: '1.6'}}>
                Transform YouTube videos into instant summaries with AI-powered technology.
                Save time, boost productivity, and never miss important content again.
              </p>
              <div style={{display: 'flex', gap: '15px'}}>
                <div style={{fontSize: '24px', cursor: 'pointer'}}>📧</div>
                <div style={{fontSize: '24px', cursor: 'pointer'}}>🐦</div>
                <div style={{fontSize: '24px', cursor: 'pointer'}}>💼</div>
                <div style={{fontSize: '24px', cursor: 'pointer'}}>📱</div>
              </div>
            </div>

            <div>
              <h3 style={{fontSize: '18px', fontWeight: '600', marginBottom: '20px'}}>Product</h3>
              <ul style={{listStyle: 'none', padding: '0', margin: '0'}}>
                {['Features', 'Pricing', 'API Documentation', 'Integrations', 'Security'].map((item, index) => (
                  <li key={index} style={{marginBottom: '10px'}}>
                    <a href="#" style={{color: '#9ca3af', textDecoration: 'none', transition: 'color 0.3s'}}
                       onMouseEnter={(e) => e.currentTarget.style.color = 'white'}
                       onMouseLeave={(e) => e.currentTarget.style.color = '#9ca3af'}>
                      {item}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h3 style={{fontSize: '18px', fontWeight: '600', marginBottom: '20px'}}>Company</h3>
              <ul style={{listStyle: 'none', padding: '0', margin: '0'}}>
                {['About Us', 'Blog', 'Careers', 'Press', 'Contact'].map((item, index) => (
                  <li key={index} style={{marginBottom: '10px'}}>
                    <a href="#" style={{color: '#9ca3af', textDecoration: 'none', transition: 'color 0.3s'}}
                       onMouseEnter={(e) => e.currentTarget.style.color = 'white'}
                       onMouseLeave={(e) => e.currentTarget.style.color = '#9ca3af'}>
                      {item}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h3 style={{fontSize: '18px', fontWeight: '600', marginBottom: '20px'}}>Support</h3>
              <ul style={{listStyle: 'none', padding: '0', margin: '0'}}>
                {['Help Center', 'Community', 'Status', 'Privacy Policy', 'Terms of Service'].map((item, index) => (
                  <li key={index} style={{marginBottom: '10px'}}>
                    <a href="#" style={{color: '#9ca3af', textDecoration: 'none', transition: 'color 0.3s'}}
                       onMouseEnter={(e) => e.currentTarget.style.color = 'white'}
                       onMouseLeave={(e) => e.currentTarget.style.color = '#9ca3af'}>
                      {item}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          <div style={{borderTop: '1px solid #374151', paddingTop: '30px', textAlign: 'center'}}>
            <p style={{color: '#6b7280', fontSize: '14px', margin: '0'}}>
              © 2024 VideoSummarizer. All rights reserved. Made with ❤️ for content creators and learners worldwide.
            </p>
          </div>
        </div>
      </footer>
      </div>
    </div>
  );
}

export default App;
