{"name": "eventemitter3", "version": "1.2.0", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "main": "index.js", "scripts": {"test-node": "istanbul cover _mocha --report lcovonly -- test.js", "coverage": "istanbul cover _mocha -- test.js", "test-browser": "zuul -- test.js", "sync": "node versions.js", "test": "mocha test.js"}, "repository": {"type": "git", "url": "git://github.com/primus/eventemitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/primus/eventemitter3/issues"}, "pre-commit": "sync, test", "devDependencies": {"assume": "1.3.x", "istanbul": "0.4.x", "mocha": "2.4.x", "pre-commit": "1.1.x", "zuul": "3.10.x"}}