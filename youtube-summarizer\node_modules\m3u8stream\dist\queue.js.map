{"version": 3, "file": "queue.js", "sourceRoot": "", "sources": ["../src/queue.ts"], "names": [], "mappings": ";;;AAOA,MAAa,KAAK;IAOhB;;;;;;OAMG;IACH,YAAY,MAAiB,EAAE,UAAoC,EAAE;QACnE,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,WAAW,IAAI,CAAC,CAAC;QAC7C,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAChB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;QACf,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IAClB,CAAC;IAGD;;;;;OAKG;IACH,IAAI,CAAC,IAAO,EAAE,QAAmB;QAC/B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;QACpC,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAGD;;OAEG;IACH,KAAK;QACH,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YAAE,OAAO;SAAE;QACvE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAa,CAAC;QACzD,IAAI,cAAc,GAAG,KAAK,CAAC;QAC3B,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;YACjC,IAAI,cAAc,EAAE;gBAAE,OAAO;aAAE;YAC/B,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,cAAc,GAAG,IAAI,CAAC;YACtB,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAG,GAAG,EAAE,MAAM,EAAE;YACxB,IAAI,CAAC,KAAK,EAAE,CAAC;QACf,CAAC,CAAC,CAAC;IACL,CAAC;IAGD;;OAEG;IACH,GAAG;QACD,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;IAClB,CAAC;CACF;AA5DD,sBA4DC"}