import React from 'react';
import { CheckIcon } from '@heroicons/react/24/outline';

const PricingSection: React.FC = () => {
  const plans = [
    {
      name: 'Free',
      price: '$0',
      period: 'forever',
      description: 'Perfect for trying out our service',
      features: [
        '5 video summaries per month',
        'Basic caption download',
        'Standard summary quality',
        'Email support',
      ],
      limitations: [
        'Limited to 10-minute videos',
        'Basic export formats',
      ],
      buttonText: 'Get Started',
      buttonStyle: 'btn-secondary',
      popular: false,
    },
    {
      name: 'Pro',
      price: '$19',
      period: 'per month',
      description: 'Ideal for content creators and professionals',
      features: [
        '100 video summaries per month',
        'Advanced caption download',
        'High-quality AI summaries',
        'Multiple export formats',
        'Priority email support',
        'Custom summary templates',
        'Batch processing',
      ],
      limitations: [],
      buttonText: 'Start Free Trial',
      buttonStyle: 'btn-primary',
      popular: true,
    },
    {
      name: 'Enterprise',
      price: '$99',
      period: 'per month',
      description: 'For teams and organizations',
      features: [
        'Unlimited video summaries',
        'Premium caption download',
        'Enterprise-grade AI summaries',
        'All export formats',
        'Dedicated support manager',
        'Custom integrations',
        'Team collaboration tools',
        'Advanced analytics',
        'API access',
        'White-label options',
      ],
      limitations: [],
      buttonText: 'Contact Sales',
      buttonStyle: 'btn-primary',
      popular: false,
    },
  ];

  return (
    <section id="pricing" className="py-24 bg-secondary-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-secondary-900 mb-4">
            Simple, Transparent Pricing
          </h2>
          <p className="text-xl text-secondary-600 max-w-2xl mx-auto">
            Choose the perfect plan for your needs. All plans include our core features with no hidden fees.
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {plans.map((plan, index) => (
            <div
              key={index}
              className={`card p-8 relative ${
                plan.popular
                  ? 'ring-2 ring-primary-500 shadow-2xl scale-105'
                  : 'hover:shadow-xl'
              } transition-all duration-300`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-primary-600 text-white px-4 py-1 rounded-full text-sm font-medium">
                    Most Popular
                  </span>
                </div>
              )}

              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-secondary-900 mb-2">{plan.name}</h3>
                <p className="text-secondary-600 mb-4">{plan.description}</p>
                <div className="mb-4">
                  <span className="text-4xl font-bold text-secondary-900">{plan.price}</span>
                  <span className="text-secondary-600 ml-2">/{plan.period}</span>
                </div>
              </div>

              <div className="space-y-4 mb-8">
                <h4 className="font-semibold text-secondary-900">What's included:</h4>
                <ul className="space-y-3">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start">
                      <CheckIcon className="h-5 w-5 text-primary-600 mt-0.5 mr-3 flex-shrink-0" />
                      <span className="text-secondary-700">{feature}</span>
                    </li>
                  ))}
                </ul>

                {plan.limitations.length > 0 && (
                  <div className="mt-6">
                    <h4 className="font-semibold text-secondary-900 mb-3">Limitations:</h4>
                    <ul className="space-y-2">
                      {plan.limitations.map((limitation, limitationIndex) => (
                        <li key={limitationIndex} className="flex items-start">
                          <span className="text-secondary-500 mr-3">•</span>
                          <span className="text-secondary-600 text-sm">{limitation}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>

              <button className={`w-full ${plan.buttonStyle} text-center`}>
                {plan.buttonText}
              </button>
            </div>
          ))}
        </div>

        <div className="mt-16 text-center">
          <p className="text-secondary-600 mb-4">
            All plans include a 14-day free trial. No credit card required.
          </p>
          <div className="flex flex-wrap justify-center gap-8 text-sm text-secondary-500">
            <span>✓ Cancel anytime</span>
            <span>✓ 99.9% uptime guarantee</span>
            <span>✓ GDPR compliant</span>
            <span>✓ 24/7 support</span>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="mt-24">
          <h3 className="text-2xl font-bold text-secondary-900 text-center mb-12">
            Frequently Asked Questions
          </h3>
          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <div className="space-y-6">
              <div>
                <h4 className="font-semibold text-secondary-900 mb-2">
                  Can I change my plan anytime?
                </h4>
                <p className="text-secondary-600">
                  Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately.
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-secondary-900 mb-2">
                  What video lengths are supported?
                </h4>
                <p className="text-secondary-600">
                  Free plan supports videos up to 10 minutes. Pro and Enterprise plans support videos of any length.
                </p>
              </div>
            </div>
            <div className="space-y-6">
              <div>
                <h4 className="font-semibold text-secondary-900 mb-2">
                  Is there an API available?
                </h4>
                <p className="text-secondary-600">
                  Yes, our API is available for Enterprise customers. Contact our sales team for more information.
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-secondary-900 mb-2">
                  What payment methods do you accept?
                </h4>
                <p className="text-secondary-600">
                  We accept all major credit cards, PayPal, and bank transfers for Enterprise customers.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PricingSection;
