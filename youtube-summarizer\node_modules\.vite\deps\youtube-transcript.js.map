{"version": 3, "sources": ["../../youtube-transcript/dist/youtube-transcript.esm.js"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nfunction __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\n\nconst RE_YOUTUBE = /(?:youtube\\.com\\/(?:[^\\/]+\\/.+\\/|(?:v|e(?:mbed)?)\\/|.*[?&]v=)|youtu\\.be\\/)([^\"&?\\/\\s]{11})/i;\r\nconst USER_AGENT = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.83 Safari/537.36,gzip(gfe)';\r\nconst RE_XML_TRANSCRIPT = /<text start=\"([^\"]*)\" dur=\"([^\"]*)\">([^<]*)<\\/text>/g;\r\nclass YoutubeTranscriptError extends Error {\r\n    constructor(message) {\r\n        super(`[YoutubeTranscript] 🚨 ${message}`);\r\n    }\r\n}\r\nclass YoutubeTranscriptTooManyRequestError extends YoutubeTranscriptError {\r\n    constructor() {\r\n        super('YouTube is receiving too many requests from this IP and now requires solving a captcha to continue');\r\n    }\r\n}\r\nclass YoutubeTranscriptVideoUnavailableError extends YoutubeTranscriptError {\r\n    constructor(videoId) {\r\n        super(`The video is no longer available (${videoId})`);\r\n    }\r\n}\r\nclass YoutubeTranscriptDisabledError extends YoutubeTranscriptError {\r\n    constructor(videoId) {\r\n        super(`Transcript is disabled on this video (${videoId})`);\r\n    }\r\n}\r\nclass YoutubeTranscriptNotAvailableError extends YoutubeTranscriptError {\r\n    constructor(videoId) {\r\n        super(`No transcripts are available for this video (${videoId})`);\r\n    }\r\n}\r\nclass YoutubeTranscriptNotAvailableLanguageError extends YoutubeTranscriptError {\r\n    constructor(lang, availableLangs, videoId) {\r\n        super(`No transcripts are available in ${lang} this video (${videoId}). Available languages: ${availableLangs.join(', ')}`);\r\n    }\r\n}\r\n/**\r\n * Class to retrieve transcript if exist\r\n */\r\nclass YoutubeTranscript {\r\n    /**\r\n     * Fetch transcript from YTB Video\r\n     * @param videoId Video url or video identifier\r\n     * @param config Get transcript in a specific language ISO\r\n     */\r\n    static fetchTranscript(videoId, config) {\r\n        var _a;\r\n        return __awaiter(this, void 0, void 0, function* () {\r\n            const identifier = this.retrieveVideoId(videoId);\r\n            const videoPageResponse = yield fetch(`https://www.youtube.com/watch?v=${identifier}`, {\r\n                headers: Object.assign(Object.assign({}, ((config === null || config === void 0 ? void 0 : config.lang) && { 'Accept-Language': config.lang })), { 'User-Agent': USER_AGENT }),\r\n            });\r\n            const videoPageBody = yield videoPageResponse.text();\r\n            const splittedHTML = videoPageBody.split('\"captions\":');\r\n            if (splittedHTML.length <= 1) {\r\n                if (videoPageBody.includes('class=\"g-recaptcha\"')) {\r\n                    throw new YoutubeTranscriptTooManyRequestError();\r\n                }\r\n                if (!videoPageBody.includes('\"playabilityStatus\":')) {\r\n                    throw new YoutubeTranscriptVideoUnavailableError(videoId);\r\n                }\r\n                throw new YoutubeTranscriptDisabledError(videoId);\r\n            }\r\n            const captions = (_a = (() => {\r\n                try {\r\n                    return JSON.parse(splittedHTML[1].split(',\"videoDetails')[0].replace('\\n', ''));\r\n                }\r\n                catch (e) {\r\n                    return undefined;\r\n                }\r\n            })()) === null || _a === void 0 ? void 0 : _a['playerCaptionsTracklistRenderer'];\r\n            if (!captions) {\r\n                throw new YoutubeTranscriptDisabledError(videoId);\r\n            }\r\n            if (!('captionTracks' in captions)) {\r\n                throw new YoutubeTranscriptNotAvailableError(videoId);\r\n            }\r\n            if ((config === null || config === void 0 ? void 0 : config.lang) &&\r\n                !captions.captionTracks.some((track) => track.languageCode === (config === null || config === void 0 ? void 0 : config.lang))) {\r\n                throw new YoutubeTranscriptNotAvailableLanguageError(config === null || config === void 0 ? void 0 : config.lang, captions.captionTracks.map((track) => track.languageCode), videoId);\r\n            }\r\n            const transcriptURL = ((config === null || config === void 0 ? void 0 : config.lang) ? captions.captionTracks.find((track) => track.languageCode === (config === null || config === void 0 ? void 0 : config.lang))\r\n                : captions.captionTracks[0]).baseUrl;\r\n            const transcriptResponse = yield fetch(transcriptURL, {\r\n                headers: Object.assign(Object.assign({}, ((config === null || config === void 0 ? void 0 : config.lang) && { 'Accept-Language': config.lang })), { 'User-Agent': USER_AGENT }),\r\n            });\r\n            if (!transcriptResponse.ok) {\r\n                throw new YoutubeTranscriptNotAvailableError(videoId);\r\n            }\r\n            const transcriptBody = yield transcriptResponse.text();\r\n            const results = [...transcriptBody.matchAll(RE_XML_TRANSCRIPT)];\r\n            return results.map((result) => {\r\n                var _a;\r\n                return ({\r\n                    text: result[3],\r\n                    duration: parseFloat(result[2]),\r\n                    offset: parseFloat(result[1]),\r\n                    lang: (_a = config === null || config === void 0 ? void 0 : config.lang) !== null && _a !== void 0 ? _a : captions.captionTracks[0].languageCode,\r\n                });\r\n            });\r\n        });\r\n    }\r\n    /**\r\n     * Retrieve video id from url or string\r\n     * @param videoId video url or video id\r\n     */\r\n    static retrieveVideoId(videoId) {\r\n        if (videoId.length === 11) {\r\n            return videoId;\r\n        }\r\n        const matchId = videoId.match(RE_YOUTUBE);\r\n        if (matchId && matchId.length) {\r\n            return matchId[1];\r\n        }\r\n        throw new YoutubeTranscriptError('Impossible to retrieve Youtube video ID.');\r\n    }\r\n}\n\nexport { YoutubeTranscript, YoutubeTranscriptDisabledError, YoutubeTranscriptError, YoutubeTranscriptNotAvailableError, YoutubeTranscriptNotAvailableLanguageError, YoutubeTranscriptTooManyRequestError, YoutubeTranscriptVideoUnavailableError };\n"], "mappings": ";;;AAeA,SAAS,UAAU,SAAS,YAAY,GAAG,WAAW;AAClD,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAG,CAAC;AAAA,EAAG;AAC3G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC1F,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC7F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IAAG;AAC7G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACxE,CAAC;AACL;AAEA,IAAM,aAAa;AACnB,IAAM,aAAa;AACnB,IAAM,oBAAoB;AAC1B,IAAM,yBAAN,cAAqC,MAAM;AAAA,EACvC,YAAY,SAAS;AACjB,UAAM,0BAA0B,OAAO,EAAE;AAAA,EAC7C;AACJ;AACA,IAAM,uCAAN,cAAmD,uBAAuB;AAAA,EACtE,cAAc;AACV,UAAM,oGAAoG;AAAA,EAC9G;AACJ;AACA,IAAM,yCAAN,cAAqD,uBAAuB;AAAA,EACxE,YAAY,SAAS;AACjB,UAAM,qCAAqC,OAAO,GAAG;AAAA,EACzD;AACJ;AACA,IAAM,iCAAN,cAA6C,uBAAuB;AAAA,EAChE,YAAY,SAAS;AACjB,UAAM,yCAAyC,OAAO,GAAG;AAAA,EAC7D;AACJ;AACA,IAAM,qCAAN,cAAiD,uBAAuB;AAAA,EACpE,YAAY,SAAS;AACjB,UAAM,gDAAgD,OAAO,GAAG;AAAA,EACpE;AACJ;AACA,IAAM,6CAAN,cAAyD,uBAAuB;AAAA,EAC5E,YAAY,MAAM,gBAAgB,SAAS;AACvC,UAAM,mCAAmC,IAAI,gBAAgB,OAAO,2BAA2B,eAAe,KAAK,IAAI,CAAC,EAAE;AAAA,EAC9H;AACJ;AAIA,IAAM,oBAAN,MAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMpB,OAAO,gBAAgB,SAAS,QAAQ;AACpC,QAAI;AACJ,WAAO,UAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,YAAM,aAAa,KAAK,gBAAgB,OAAO;AAC/C,YAAM,oBAAoB,MAAM,MAAM,mCAAmC,UAAU,IAAI;AAAA,QACnF,SAAS,OAAO,OAAO,OAAO,OAAO,CAAC,IAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,SAAS,EAAE,mBAAmB,OAAO,KAAK,CAAE,GAAG,EAAE,cAAc,WAAW,CAAC;AAAA,MACjL,CAAC;AACD,YAAM,gBAAgB,MAAM,kBAAkB,KAAK;AACnD,YAAM,eAAe,cAAc,MAAM,aAAa;AACtD,UAAI,aAAa,UAAU,GAAG;AAC1B,YAAI,cAAc,SAAS,qBAAqB,GAAG;AAC/C,gBAAM,IAAI,qCAAqC;AAAA,QACnD;AACA,YAAI,CAAC,cAAc,SAAS,sBAAsB,GAAG;AACjD,gBAAM,IAAI,uCAAuC,OAAO;AAAA,QAC5D;AACA,cAAM,IAAI,+BAA+B,OAAO;AAAA,MACpD;AACA,YAAM,YAAY,MAAM,MAAM;AAC1B,YAAI;AACA,iBAAO,KAAK,MAAM,aAAa,CAAC,EAAE,MAAM,gBAAgB,EAAE,CAAC,EAAE,QAAQ,MAAM,EAAE,CAAC;AAAA,QAClF,SACO,GAAG;AACN,iBAAO;AAAA,QACX;AAAA,MACJ,GAAG,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,iCAAiC;AAC/E,UAAI,CAAC,UAAU;AACX,cAAM,IAAI,+BAA+B,OAAO;AAAA,MACpD;AACA,UAAI,EAAE,mBAAmB,WAAW;AAChC,cAAM,IAAI,mCAAmC,OAAO;AAAA,MACxD;AACA,WAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,SACxD,CAAC,SAAS,cAAc,KAAK,CAAC,UAAU,MAAM,kBAAkB,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,KAAK,GAAG;AAC/H,cAAM,IAAI,2CAA2C,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,MAAM,SAAS,cAAc,IAAI,CAAC,UAAU,MAAM,YAAY,GAAG,OAAO;AAAA,MACxL;AACA,YAAM,kBAAkB,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,QAAQ,SAAS,cAAc,KAAK,CAAC,UAAU,MAAM,kBAAkB,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,KAAK,IAC5M,SAAS,cAAc,CAAC,GAAG;AACjC,YAAM,qBAAqB,MAAM,MAAM,eAAe;AAAA,QAClD,SAAS,OAAO,OAAO,OAAO,OAAO,CAAC,IAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,SAAS,EAAE,mBAAmB,OAAO,KAAK,CAAE,GAAG,EAAE,cAAc,WAAW,CAAC;AAAA,MACjL,CAAC;AACD,UAAI,CAAC,mBAAmB,IAAI;AACxB,cAAM,IAAI,mCAAmC,OAAO;AAAA,MACxD;AACA,YAAM,iBAAiB,MAAM,mBAAmB,KAAK;AACrD,YAAM,UAAU,CAAC,GAAG,eAAe,SAAS,iBAAiB,CAAC;AAC9D,aAAO,QAAQ,IAAI,CAAC,WAAW;AAC3B,YAAIA;AACJ,eAAQ;AAAA,UACJ,MAAM,OAAO,CAAC;AAAA,UACd,UAAU,WAAW,OAAO,CAAC,CAAC;AAAA,UAC9B,QAAQ,WAAW,OAAO,CAAC,CAAC;AAAA,UAC5B,OAAOA,MAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,UAAU,QAAQA,QAAO,SAASA,MAAK,SAAS,cAAc,CAAC,EAAE;AAAA,QACxI;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,gBAAgB,SAAS;AAC5B,QAAI,QAAQ,WAAW,IAAI;AACvB,aAAO;AAAA,IACX;AACA,UAAM,UAAU,QAAQ,MAAM,UAAU;AACxC,QAAI,WAAW,QAAQ,QAAQ;AAC3B,aAAO,QAAQ,CAAC;AAAA,IACpB;AACA,UAAM,IAAI,uBAAuB,0CAA0C;AAAA,EAC/E;AACJ;", "names": ["_a"]}