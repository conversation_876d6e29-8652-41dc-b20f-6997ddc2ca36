{"name": "m3u8stream", "description": "Reads segments from a m3u8 or dash playlist into a consumable stream.", "keywords": ["m3u8", "hls", "dash", "live", "playlist", "segments", "stream"], "version": "0.8.6", "repository": {"type": "git", "url": "git://github.com/fent/node-m3u8stream.git"}, "author": "fent <<EMAIL>> (https://github.com/fent)", "main": "./dist/index.js", "files": ["dist"], "scripts": {"prepare": "tsc -p tsconfig.build.json", "build": "tsc -p tsconfig.build.json", "test": "nyc --extension .ts --reporter=lcov --reporter=text-summary npm run test:unit", "test:unit": "mocha -- --require ts-node/register test/*-test.ts", "lint": "eslint ./src ./test", "lint:fix": "eslint --fix ./src ./test"}, "dependencies": {"miniget": "^4.2.2", "sax": "^1.2.4"}, "devDependencies": {"@types/mocha": "^7.0.0", "@types/node": "^17.0.8", "@types/sax": "^1.0.1", "@types/sinon": "^9.0.8", "@typescript-eslint/eslint-plugin": "^4.8.2", "@typescript-eslint/parser": "^4.8.2", "eslint": "^7.14.0", "mocha": "^7.0.1", "nock": "^13.0.5", "nyc": "^15.0.0", "sinon": "^9.2.0", "ts-node": "^9.0.0", "typescript": "^4.0.5"}, "engines": {"node": ">=12"}, "license": "MIT"}