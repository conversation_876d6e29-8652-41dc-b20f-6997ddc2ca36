{"name": "http-proxy", "version": "1.11.1", "repository": {"type": "git", "url": "https://github.com/nodejitsu/node-http-proxy.git"}, "description": "HTTP proxying for the masses", "author": "Nodejitsu Inc. <<EMAIL>>", "maintainers": ["yawnt <<EMAIL>>", "indexzero <<EMAIL>>"], "main": "index.js", "dependencies": {"eventemitter3": "1.x.x", "requires-port": "0.x.x"}, "devDependencies": {"async": "*", "blanket": "*", "coveralls": "*", "dox": "*", "expect.js": "*", "mocha": "*", "mocha-lcov-reporter": "*", "semver": "^4.3.3", "socket.io": "*", "socket.io-client": "*", "ws": "~0.5.0"}, "scripts": {"coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js", "blanket": {"pattern": "lib/http-proxy"}, "test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html"}, "engines": {"node": ">=0.10.0"}, "license": "MIT"}